# MetaTrader5 + VPS2 集成Docker镜像

这个项目将MetaTrader5-Docker-Image与VPS2自动交易系统完全集成，提供一个完整的交易解决方案。

## 🚀 功能特性

### MetaTrader5功能
- 基于Wine的MetaTrader5运行环境
- Web VNC界面访问MetaTrader5
- RPyC服务器支持远程Python编程
- 自动安装和配置

### VPS2自动交易功能
- 交易信号接收和处理
- 自动执行MT5交易
- Web管理界面
- 余额监控和通知
- 多种交易策略支持
- Bark推送通知

## 📋 端口说明

| 端口 | 服务 | 说明 |
|------|------|------|
| 3000 | MetaTrader5 VNC | Web界面访问MetaTrader5 |
| 8001 | MT5Linux RPyC | Python远程编程接口 |
| 8080 | VPS2 Web管理 | VPS2交易系统管理界面 |
| 9999 | VPS2 信号接收 | 接收交易信号的API端点 |

## 🛠️ 快速开始

### 1. 准备配置文件

复制环境变量模板：
```bash
cp .env.example .env
```

编辑`.env`文件，设置您的用户名和密码：
```bash
# MetaTrader5 VNC访问配置
CUSTOM_USER=admin
PASSWORD=your_secure_password

# VPS2管理界面配置
VPS2_ADMIN_USERNAME=admin
VPS2_ADMIN_PASSWORD=668899asd
```

### 2. 配置MT5交易账户

编辑`vps2/config.json`文件，配置您的MT5账户信息：
```json
{
    "mt5_login": "您的MT5账户",
    "mt5_password": "您的MT5密码",
    "mt5_server": "您的MT5服务器",
    "enable_trading": true,
    ...
}
```

### 3. 启动服务

使用Docker Compose启动：
```bash
docker-compose up -d
```

### 4. 访问服务

启动完成后，您可以通过以下地址访问各个服务：

- **MetaTrader5 Web界面**: http://localhost:3000
- **VPS2 管理界面**: http://localhost:8080
- **信号接收API**: http://localhost:9999

## 📖 详细使用说明

### MetaTrader5使用

1. 访问 http://localhost:3000
2. 使用您在`.env`中设置的用户名和密码登录
3. 等待MetaTrader5自动安装和启动（首次启动需要5-10分钟）
4. 在MetaTrader5中登录您的交易账户

### VPS2管理界面

1. 访问 http://localhost:8080
2. 使用默认用户名`admin`和密码`668899asd`登录
3. 在设置页面配置您的交易参数
4. 启用交易功能

### 发送交易信号

向信号接收API发送POST请求：
```bash
curl -X POST http://localhost:9999/trade_signal \
  -H "Content-Type: application/json" \
  -d '{
    "trading_pair": "EURUSD",
    "signal_type": "buy",
    "close_price": 1.1000
  }'
```

## 🔧 配置说明

### 主要配置文件

- `vps2/config.json`: VPS2主配置文件
- `.env`: Docker环境变量
- `docker-compose.yaml`: Docker服务配置

### 重要配置项

#### MT5连接配置
```json
{
    "mt5_login": "您的MT5账户",
    "mt5_password": "您的MT5密码", 
    "mt5_server": "您的MT5服务器",
    "enable_trading": true
}
```

#### 交易参数配置
```json
{
    "default_volume": 0.1,
    "default_sl_points": 400,
    "default_tp_points": 400,
    "symbol_volumes": {
        "EURUSD": 0.1,
        "GBPUSD": 0.1
    }
}
```

#### 通知配置
```json
{
    "bark_url": "https://api.day.app",
    "bark_device_key": "您的Bark设备密钥",
    "bark_notifications": {
        "signal_received": true,
        "trade_execution": true,
        "error": true
    }
}
```

## 🐛 故障排除

### 查看日志

```bash
# 查看容器日志
docker-compose logs -f

# 查看VPS2服务日志
docker exec -it mt5_vps2 tail -f /vps2/logs/web_interface.log
docker exec -it mt5_vps2 tail -f /vps2/logs/signal_receiver.log
docker exec -it mt5_vps2 tail -f /vps2/logs/mt5_trader.log
```

### 常见问题

1. **MetaTrader5无法连接**
   - 检查MT5账户信息是否正确
   - 确认网络连接正常
   - 查看MT5日志

2. **VPS2服务无法启动**
   - 检查配置文件格式是否正确
   - 确认端口没有被占用
   - 查看VPS2日志

3. **交易信号无响应**
   - 确认交易功能已启用
   - 检查信号格式是否正确
   - 查看信号接收日志

## 📁 文件结构

```
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yaml     # Docker Compose配置
├── .env.example            # 环境变量模板
├── Metatrader/             # MetaTrader5启动脚本
├── scripts/                # 集成启动脚本
│   ├── start_vps2.sh      # VPS2服务启动脚本
│   └── init_vps2_config.py # VPS2配置初始化
├── vps2/                   # VPS2项目文件
│   ├── config.json        # VPS2配置文件
│   ├── main.py            # VPS2主程序
│   ├── web_interface.py   # Web管理界面
│   ├── signal_receiver.py # 信号接收服务
│   ├── mt5_trader.py      # MT5交易模块
│   └── ...                # 其他VPS2文件
└── config/                 # 挂载的配置目录
```

## 🔒 安全注意事项

1. 修改默认密码
2. 使用强密码
3. 限制网络访问
4. 定期备份配置和数据
5. 监控交易活动

## 📞 支持

如果您遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除部分
4. 提交Issue描述问题
