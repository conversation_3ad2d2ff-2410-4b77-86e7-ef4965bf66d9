version: '3'

services:
  mt5:
    build: .
    image: gmag11/metatrader5_vnc_vps2
    container_name: mt5_vps2
    volumes:
      - ./config:/config
      - ./vps2/config.json:/vps2/config.json
      - ./vps2/logs:/vps2/logs
    ports:
      - 3000:3000    # MetaTrader5 VNC Web界面
      - 8001:8001    # MT5Linux RPyC服务器
      - 8080:8080    # VPS2 Web管理界面
      - 9999:9999    # VPS2 信号接收服务
    environment:
      - CUSTOM_USER=${CUSTOM_USER:-admin}
      - PASSWORD=${PASSWORD:-password}
      - VPS2_ADMIN_USERNAME=${VPS2_ADMIN_USERNAME:-admin}
      - VPS2_ADMIN_PASSWORD=${VPS2_ADMIN_PASSWORD:-668899asd}
    env_file:
      - .env
  
