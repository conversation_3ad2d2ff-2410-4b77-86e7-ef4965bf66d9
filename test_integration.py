#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 验证VPS2与MetaTrader5 Docker集成配置
"""

import os
import json
import sys
from pathlib import Path

def test_file_structure():
    """测试文件结构"""
    print("=== 测试文件结构 ===")
    
    required_files = [
        "Dockerfile",
        "docker-compose.yaml",
        "scripts/start_vps2.sh",
        "scripts/init_vps2_config.py",
        "vps2/config.json",
        "vps2/config_docker.json",
        "vps2/main.py",
        "vps2/web_interface.py",
        "vps2/signal_receiver.py",
        "vps2/mt5_trader.py",
        "Metatrader/start.sh",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def test_dockerfile():
    """测试Dockerfile配置"""
    print("\n=== 测试Dockerfile ===")
    
    with open("Dockerfile", "r") as f:
        content = f.read()
    
    required_elements = [
        "COPY /vps2 /vps2",
        "pip3 install --no-cache-dir",
        "MetaTrader5",
        "flask",
        "EXPOSE 3000 8001 8080 9999",
        "COPY /scripts /scripts"
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in content:
            missing_elements.append(element)
        else:
            print(f"✅ 包含: {element}")
    
    if missing_elements:
        print(f"❌ Dockerfile缺少: {missing_elements}")
        return False
    
    print("✅ Dockerfile配置正确")
    return True

def test_docker_compose():
    """测试docker-compose.yaml配置"""
    print("\n=== 测试docker-compose.yaml ===")
    
    with open("docker-compose.yaml", "r") as f:
        content = f.read()
    
    required_ports = ["3000:3000", "8001:8001", "8080:8080", "9999:9999"]
    
    missing_ports = []
    for port in required_ports:
        if port not in content:
            missing_ports.append(port)
        else:
            print(f"✅ 端口映射: {port}")
    
    if missing_ports:
        print(f"❌ 缺少端口映射: {missing_ports}")
        return False
    
    print("✅ docker-compose.yaml配置正确")
    return True

def test_vps2_config():
    """测试VPS2配置文件"""
    print("\n=== 测试VPS2配置 ===")
    
    # 测试config.json
    try:
        with open("vps2/config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 检查MT5路径是否适配Docker
        mt5_path = config.get("mt5_path")
        expected_path = "/config/.wine/drive_c/Program Files/MetaTrader 5/terminal64.exe"
        
        if mt5_path == expected_path:
            print("✅ MT5路径配置正确")
        else:
            print(f"❌ MT5路径错误: {mt5_path}")
            print(f"   期望: {expected_path}")
            return False
        
        # 检查端口配置
        if config.get("server_port") == 9999:
            print("✅ 信号接收端口配置正确")
        else:
            print(f"❌ 信号接收端口错误: {config.get('server_port')}")
        
        if config.get("web_port") == 8080:
            print("✅ Web端口配置正确")
        else:
            print(f"❌ Web端口错误: {config.get('web_port')}")
        
    except Exception as e:
        print(f"❌ 读取config.json失败: {e}")
        return False
    
    # 测试config_docker.json
    try:
        with open("vps2/config_docker.json", "r", encoding="utf-8") as f:
            docker_config = json.load(f)
        print("✅ Docker配置文件格式正确")
    except Exception as e:
        print(f"❌ 读取config_docker.json失败: {e}")
        return False
    
    print("✅ VPS2配置文件正确")
    return True

def test_scripts():
    """测试启动脚本"""
    print("\n=== 测试启动脚本 ===")
    
    # 检查脚本权限
    scripts = ["scripts/start_vps2.sh"]
    
    for script in scripts:
        if os.path.exists(script):
            print(f"✅ 脚本存在: {script}")
            # 检查脚本内容
            with open(script, "r") as f:
                content = f.read()
            
            if "python3 signal_receiver.py" in content:
                print("✅ 包含信号接收服务启动")
            if "python3 mt5_trader.py" in content:
                print("✅ 包含MT5交易服务启动")
            if "python3 web_interface.py" in content:
                print("✅ 包含Web界面服务启动")
        else:
            print(f"❌ 脚本不存在: {script}")
            return False
    
    # 检查MetaTrader启动脚本
    with open("Metatrader/start.sh", "r") as f:
        content = f.read()
    
    if "/scripts/start_vps2.sh" in content:
        print("✅ MetaTrader启动脚本包含VPS2启动")
    else:
        print("❌ MetaTrader启动脚本未包含VPS2启动")
        return False
    
    print("✅ 启动脚本配置正确")
    return True

def test_vps2_dependencies():
    """测试VPS2依赖"""
    print("\n=== 测试VPS2依赖 ===")
    
    with open("vps2/requirements.txt", "r") as f:
        requirements = f.read()
    
    required_packages = ["MetaTrader5", "flask", "werkzeug", "requests", "ntplib", "pytz"]
    
    missing_packages = []
    for package in required_packages:
        if package in requirements:
            print(f"✅ 依赖包: {package}")
        else:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {missing_packages}")
        return False
    
    print("✅ VPS2依赖包完整")
    return True

def main():
    """主测试函数"""
    print("🚀 开始集成测试...")
    
    tests = [
        test_file_structure,
        test_dockerfile,
        test_docker_compose,
        test_vps2_config,
        test_scripts,
        test_vps2_dependencies
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            failed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！集成配置正确。")
        print("\n📋 下一步操作:")
        print("1. 复制 .env.example 到 .env 并配置您的参数")
        print("2. 编辑 vps2/config.json 配置您的MT5账户信息")
        print("3. 运行 docker-compose up -d 启动服务")
        print("4. 访问以下地址:")
        print("   - MetaTrader5 VNC: http://localhost:3000")
        print("   - VPS2 管理界面: http://localhost:8080")
        print("   - 信号接收API: http://localhost:9999")
        return True
    else:
        print("❌ 存在配置问题，请修复后重新测试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
