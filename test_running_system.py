#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行系统测试脚本 - 测试正在运行的VPS2系统
"""

import requests
import json
import time
import sys

def test_signal_receiver():
    """测试信号接收服务"""
    print("=== 测试信号接收服务 ===")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:9999/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过")
            print(f"   状态: {data.get('status')}")
            print(f"   端口: {data.get('port')}")
            print(f"   交易开关: {'开启' if data.get('trading_enabled') else '关闭'}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
        
        # 测试信号接收
        signal_data = {
            "trading_pair": "EURUSD",
            "signal_type": "buy",
            "close_price": 1.1000
        }
        
        response = requests.post(
            "http://localhost:9999/trade_signal",
            json=signal_data,
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 信号发送成功: {result.get('message')}")
        else:
            print(f"❌ 信号发送失败: {response.status_code}")
            return False
        
        # 测试信号历史
        response = requests.get("http://localhost:9999/signals", timeout=5)
        if response.status_code == 200:
            signals = response.json()
            print(f"✅ 信号历史查询成功，共 {len(signals)} 条记录")
        else:
            print(f"❌ 信号历史查询失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号接收服务测试失败: {e}")
        return False

def test_vnc_server():
    """测试VNC服务器"""
    print("\n=== 测试VNC服务器 ===")
    
    try:
        import socket
        
        # 测试VNC端口连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 5900))
        sock.close()
        
        if result == 0:
            print("✅ VNC服务器端口5900可连接")
            print("   可以使用VNC客户端连接: vncviewer localhost:5900")
            return True
        else:
            print("❌ VNC服务器端口5900不可连接")
            return False
            
    except Exception as e:
        print(f"❌ VNC服务器测试失败: {e}")
        return False

def test_container_processes():
    """测试容器内进程"""
    print("\n=== 测试容器进程 ===")
    
    try:
        import subprocess
        
        # 获取容器内进程列表
        result = subprocess.run(
            ['sudo', 'docker', 'exec', 'vps2-simple-test', 'ps', 'aux'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            processes = result.stdout
            
            # 检查关键进程
            critical_processes = [
                ('Xvfb', 'X虚拟显示服务'),
                ('fluxbox', '窗口管理器'),
                ('x11vnc', 'VNC服务器'),
                ('signal_receiver.py', '信号接收服务'),
            ]
            
            all_running = True
            for process_name, description in critical_processes:
                if process_name in processes:
                    print(f"✅ {description} 运行正常")
                else:
                    print(f"❌ {description} 未运行")
                    all_running = False
            
            return all_running
        else:
            print(f"❌ 无法获取容器进程信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 容器进程测试失败: {e}")
        return False

def test_database():
    """测试数据库功能"""
    print("\n=== 测试数据库功能 ===")
    
    try:
        import subprocess
        
        # 检查数据库文件是否存在
        result = subprocess.run(
            ['sudo', 'docker', 'exec', 'vps2-simple-test', 'ls', '-la', '/vps2/trading_data.db'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ 数据库文件存在")
            
            # 检查数据库表
            result = subprocess.run(
                ['sudo', 'docker', 'exec', 'vps2-simple-test', 'sqlite3', '/vps2/trading_data.db', '.tables'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                tables = result.stdout.strip()
                print(f"✅ 数据库表: {tables}")
                
                # 检查信号记录
                result = subprocess.run(
                    ['sudo', 'docker', 'exec', 'vps2-simple-test', 'sqlite3', '/vps2/trading_data.db', 
                     'SELECT COUNT(*) FROM signals;'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    count = result.stdout.strip()
                    print(f"✅ 信号记录数量: {count}")
                    return True
                else:
                    print(f"❌ 无法查询信号记录: {result.stderr}")
                    return False
            else:
                print(f"❌ 无法查询数据库表: {result.stderr}")
                return False
        else:
            print("❌ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_logs():
    """测试日志功能"""
    print("\n=== 测试日志功能 ===")
    
    try:
        import subprocess
        
        # 检查日志目录
        result = subprocess.run(
            ['sudo', 'docker', 'exec', 'vps2-simple-test', 'ls', '-la', '/vps2/logs/'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ 日志目录存在")
            print("   日志文件:")
            for line in result.stdout.split('\n'):
                if '.log' in line:
                    print(f"     {line.strip()}")
            
            # 检查信号接收日志
            result = subprocess.run(
                ['sudo', 'docker', 'exec', 'vps2-simple-test', 'tail', '-5', '/vps2/logs/signal_receiver.out.log'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("✅ 信号接收日志正常")
                print("   最新日志:")
                for line in result.stdout.split('\n')[-3:]:
                    if line.strip():
                        print(f"     {line.strip()}")
            
            return True
        else:
            print(f"❌ 无法访问日志目录: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 日志测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始运行系统测试...")
    print("=" * 60)
    
    tests = [
        test_signal_receiver,
        test_vnc_server,
        test_container_processes,
        test_database,
        test_logs
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{len(tests)} 通过")
    
    if failed == 0:
        print("🎉 所有测试通过！VPS2系统运行正常。")
        print("\n📋 系统状态:")
        print("✅ 信号接收服务: http://localhost:9999")
        print("✅ VNC服务器: localhost:5900")
        print("✅ 数据库和日志功能正常")
        
        print("\n🔧 下一步:")
        print("1. 使用VNC客户端连接: vncviewer localhost:5900")
        print("2. 发送测试信号: curl -X POST http://localhost:9999/trade_signal \\")
        print("   -H 'Content-Type: application/json' \\")
        print("   -d '{\"trading_pair\": \"EURUSD\", \"signal_type\": \"buy\", \"close_price\": 1.1000}'")
        print("3. 查看信号历史: curl http://localhost:9999/signals")
        
        return True
    else:
        print("❌ 部分测试失败，请检查系统状态。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
