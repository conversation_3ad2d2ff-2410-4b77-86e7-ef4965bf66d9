FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV WINEPREFIX="/config/.wine"
ENV TITLE=Metatrader5

# 更新系统并安装基础包
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y \
    python3 \
    python3-pip \
    wget \
    curl \
    sqlite3 \
    software-properties-common \
    gnupg2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装VPS2 Python依赖 (除了MetaTrader5，因为它只在Windows下可用)
RUN pip3 install --no-cache-dir \
    flask \
    werkzeug \
    requests \
    ntplib \
    pytz \
    Pillow

# 复制VPS2项目文件
COPY /vps2 /vps2
WORKDIR /vps2

# 创建日志目录
RUN mkdir -p /vps2/logs

# 复制启动脚本
COPY /scripts /scripts
RUN chmod +x /scripts/*.sh /scripts/*.py

# 暴露端口
EXPOSE 8080 9999

# 创建启动脚本
RUN echo '#!/bin/bash\n\
cd /vps2\n\
python3 /scripts/init_vps2_config.py\n\
echo "Starting VPS2 services..."\n\
python3 signal_receiver.py &\n\
sleep 5\n\
python3 mt5_trader.py &\n\
sleep 5\n\
python3 web_interface.py &\n\
echo "All services started"\n\
tail -f /dev/null' > /start_vps2_only.sh && \
chmod +x /start_vps2_only.sh

CMD ["/start_vps2_only.sh"]
