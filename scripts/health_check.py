#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查脚本 - 验证所有服务是否正常运行
"""

import requests
import time
import sys
import subprocess
import json

def check_port(port, service_name):
    """检查端口是否开放"""
    try:
        result = subprocess.run(['ss', '-tuln'], capture_output=True, text=True)
        if f":{port}" in result.stdout:
            print(f"✅ {service_name} 端口 {port} 已开放")
            return True
        else:
            print(f"❌ {service_name} 端口 {port} 未开放")
            return False
    except Exception as e:
        print(f"❌ 检查端口 {port} 失败: {e}")
        return False

def check_http_service(url, service_name, timeout=5):
    """检查HTTP服务是否响应"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {service_name} HTTP服务正常 ({url})")
            return True
        else:
            print(f"⚠️ {service_name} HTTP服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {service_name} 连接失败 ({url})")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {service_name} 连接超时 ({url})")
        return False
    except Exception as e:
        print(f"❌ {service_name} 检查失败: {e}")
        return False

def check_vps2_api():
    """检查VPS2 API功能"""
    try:
        # 检查健康状态
        response = requests.get("http://localhost:9999/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ VPS2 API健康检查通过")
            print(f"   状态: {data.get('status', 'unknown')}")
            print(f"   交易开关: {'开启' if data.get('trading_enabled') else '关闭'}")
            return True
        else:
            print(f"❌ VPS2 API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ VPS2 API检查失败: {e}")
        return False

def check_vps2_web():
    """检查VPS2 Web界面"""
    try:
        response = requests.get("http://localhost:8080/login", timeout=5)
        if response.status_code == 200:
            print("✅ VPS2 Web界面正常")
            return True
        else:
            print(f"❌ VPS2 Web界面异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ VPS2 Web界面检查失败: {e}")
        return False

def check_mt5_vnc():
    """检查MetaTrader5 VNC界面"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ MetaTrader5 VNC界面正常")
            return True
        else:
            print(f"❌ MetaTrader5 VNC界面异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MetaTrader5 VNC界面检查失败: {e}")
        return False

def check_processes():
    """检查关键进程是否运行"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        processes = result.stdout
        
        critical_processes = [
            ('python3 signal_receiver.py', 'VPS2信号接收服务'),
            ('python3 mt5_trader.py', 'VPS2交易服务'),
            ('python3 web_interface.py', 'VPS2 Web服务'),
            ('wine', 'Wine环境'),
        ]
        
        all_running = True
        for process_cmd, process_name in critical_processes:
            if process_cmd in processes:
                print(f"✅ {process_name} 进程运行中")
            else:
                print(f"❌ {process_name} 进程未运行")
                all_running = False
        
        return all_running
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def check_config_files():
    """检查配置文件"""
    config_files = [
        ('/vps2/config.json', 'VPS2配置文件'),
        ('/vps2/trading_data.db', 'VPS2数据库')
    ]
    
    all_exist = True
    for file_path, file_name in config_files:
        if os.path.exists(file_path):
            print(f"✅ {file_name} 存在")
        else:
            print(f"❌ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主健康检查函数"""
    print("🔍 开始健康检查...")
    print("=" * 50)
    
    checks = []
    
    # 端口检查
    print("\n📡 端口检查:")
    checks.append(check_port(3000, "MetaTrader5 VNC"))
    checks.append(check_port(8001, "MT5Linux RPyC"))
    checks.append(check_port(8080, "VPS2 Web"))
    checks.append(check_port(9999, "VPS2 API"))
    
    # HTTP服务检查
    print("\n🌐 HTTP服务检查:")
    checks.append(check_mt5_vnc())
    checks.append(check_vps2_web())
    checks.append(check_vps2_api())
    
    # 进程检查
    print("\n⚙️ 进程检查:")
    checks.append(check_processes())
    
    # 配置文件检查
    print("\n📁 配置文件检查:")
    checks.append(check_config_files())
    
    # 总结
    print("\n" + "=" * 50)
    passed = sum(checks)
    total = len(checks)
    
    print(f"📊 健康检查结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有检查通过！系统运行正常。")
        print("\n🌐 访问地址:")
        print("   - MetaTrader5 VNC: http://localhost:3000")
        print("   - VPS2 管理界面: http://localhost:8080")
        print("   - VPS2 API: http://localhost:9999")
        return True
    else:
        print("⚠️ 部分检查失败，请查看上述详细信息。")
        print("\n🔧 故障排除建议:")
        print("   1. 检查Docker容器是否正常运行: docker-compose ps")
        print("   2. 查看服务日志: docker-compose logs -f")
        print("   3. 重启服务: docker-compose restart")
        return False

if __name__ == "__main__":
    import os
    success = main()
    sys.exit(0 if success else 1)
