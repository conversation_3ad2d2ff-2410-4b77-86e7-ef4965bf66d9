#!/bin/bash

# 集成启动脚本 - 启动VNC、Wine、MetaTrader5和VPS2服务

echo "=== MetaTrader5 + VPS2 集成系统启动 ==="

# 配置变量
mt5file='/config/.wine/drive_c/Program Files/MetaTrader 5/terminal64.exe'
WINEPREFIX='/config/.wine'
wine_executable="wine"
metatrader_version="5.0.36"
mt5server_port="8001"
mono_url="https://dl.winehq.org/wine/wine-mono/8.0.0/wine-mono-8.0.0-x86.msi"
python_url="https://www.python.org/ftp/python/3.9.0/python-3.9.0.exe"
mt5setup_url="https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.exe"

# 创建必要的目录
mkdir -p /config/.wine
mkdir -p /vps2/logs

# 函数：显示消息
show_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 函数：检查依赖
check_dependency() {
    if ! command -v $1 &> /dev/null; then
        echo "$1 is not installed. Please install it to continue."
        exit 1
    fi
}

# 函数：检查Python包是否安装在Wine中
is_wine_python_package_installed() {
    $wine_executable python -c "import pkg_resources; exit(not pkg_resources.require('$1'))" 2>/dev/null
    return $?
}

# 检查必要依赖
check_dependency "curl"
check_dependency "$wine_executable"

# 启动Xvfb (虚拟显示)
show_message "[1/10] 启动虚拟显示服务..."
Xvfb :99 -screen 0 1024x768x24 &
export DISPLAY=:99
sleep 2

# 启动窗口管理器
show_message "[2/10] 启动窗口管理器..."
fluxbox &
sleep 2

# 启动VNC服务器
show_message "[3/10] 启动VNC服务器..."
x11vnc -display :99 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever -shared &
sleep 2

# 安装Mono (如果需要)
if [ ! -e "/config/.wine/drive_c/windows/mono" ]; then
    show_message "[4/10] 下载并安装Mono..."
    curl -o /config/.wine/drive_c/mono.msi $mono_url
    WINEDLLOVERRIDES=mscoree=d $wine_executable msiexec /i /config/.wine/drive_c/mono.msi /qn
    rm /config/.wine/drive_c/mono.msi
    show_message "[4/10] Mono安装完成"
else
    show_message "[4/10] Mono已安装"
fi

# 检查并安装MetaTrader 5
if [ -e "$mt5file" ]; then
    show_message "[5/10] MetaTrader 5已安装"
else
    show_message "[5/10] 安装MetaTrader 5..."
    
    # 设置Windows 10模式
    $wine_executable reg add "HKEY_CURRENT_USER\\Software\\Wine" /v Version /t REG_SZ /d "win10" /f
    
    # 下载并安装MT5
    show_message "[5/10] 下载MT5安装程序..."
    curl -o /config/.wine/drive_c/mt5setup.exe $mt5setup_url
    
    show_message "[5/10] 安装MetaTrader 5..."
    $wine_executable "/config/.wine/drive_c/mt5setup.exe" "/auto" &
    wait
    rm -f /config/.wine/drive_c/mt5setup.exe
fi

# 启动MetaTrader 5
if [ -e "$mt5file" ]; then
    show_message "[6/10] 启动MetaTrader 5..."
    $wine_executable "$mt5file" &
    sleep 10
else
    show_message "[6/10] MetaTrader 5未找到，跳过启动"
fi

# 安装Wine中的Python (如果需要)
if ! $wine_executable python --version 2>/dev/null; then
    show_message "[7/10] 在Wine中安装Python..."
    curl -L $python_url -o /tmp/python-installer.exe
    $wine_executable /tmp/python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
    rm /tmp/python-installer.exe
    show_message "[7/10] Wine中的Python安装完成"
else
    show_message "[7/10] Wine中的Python已安装"
fi

# 安装Python库
show_message "[8/10] 安装Python库..."
$wine_executable python -m pip install --upgrade --no-cache-dir pip

# 安装MetaTrader5库
if ! is_wine_python_package_installed "MetaTrader5==$metatrader_version"; then
    $wine_executable python -m pip install --no-cache-dir MetaTrader5==$metatrader_version
fi

# 安装mt5linux库
if ! is_wine_python_package_installed "mt5linux"; then
    $wine_executable python -m pip install --no-cache-dir mt5linux
fi

# 启动MT5Linux服务器
show_message "[9/10] 启动MT5Linux服务器..."
python3 -m mt5linux --host 0.0.0.0 -p $mt5server_port -w $wine_executable python.exe &

# 等待服务器启动
sleep 5

# 检查MT5Linux服务器是否运行
if ss -tuln | grep ":$mt5server_port" > /dev/null; then
    show_message "[9/10] MT5Linux服务器运行正常 (端口 $mt5server_port)"
else
    show_message "[9/10] MT5Linux服务器启动失败"
fi

# 启动VPS2服务
show_message "[10/10] 启动VPS2服务..."
cd /vps2

# 初始化VPS2配置
python3 /scripts/init_vps2_config.py

# 启动VPS2服务
echo "启动信号接收服务 (端口9999)..."
python3 signal_receiver.py > logs/signal_receiver.out.log 2> logs/signal_receiver.err.log &
SIGNAL_PID=$!

sleep 5

echo "启动MT5交易模块 (使用mt5linux)..."
python3 mt5_trader_linux.py > logs/mt5_trader.out.log 2> logs/mt5_trader.err.log &
MT5_TRADER_PID=$!

sleep 5

echo "启动VPS2 Web管理界面 (端口8080)..."
python3 web_interface.py > logs/web_interface.out.log 2> logs/web_interface.err.log &
WEB_PID=$!

sleep 10

# 检查服务状态
echo "=== 服务状态检查 ==="

# 检查VNC
if pgrep -f "x11vnc" > /dev/null; then
    echo "✅ VNC服务器运行正常"
else
    echo "❌ VNC服务器未运行"
fi

# 检查MetaTrader5
if pgrep -f "terminal64.exe" > /dev/null; then
    echo "✅ MetaTrader5运行正常"
else
    echo "❌ MetaTrader5未运行"
fi

# 检查MT5Linux服务器
if ss -tuln | grep ":$mt5server_port" > /dev/null; then
    echo "✅ MT5Linux服务器运行正常 (端口 $mt5server_port)"
else
    echo "❌ MT5Linux服务器未运行"
fi

# 检查VPS2服务
if ss -tuln | grep ":9999" > /dev/null; then
    echo "✅ 信号接收服务运行正常 (端口9999)"
else
    echo "❌ 信号接收服务未运行"
fi

if ss -tuln | grep ":8080" > /dev/null; then
    echo "✅ VPS2 Web界面运行正常 (端口8080)"
else
    echo "❌ VPS2 Web界面未运行"
fi

echo "=== 系统启动完成 ==="
echo "访问地址："
echo "- VNC (通过VNC客户端): localhost:5900"
echo "- VPS2 Web管理: http://localhost:8080"
echo "- 信号接收API: http://localhost:9999"
echo "- MT5Linux RPyC: localhost:8001"

# 保持脚本运行
echo "系统运行中，按Ctrl+C停止..."
while true; do
    sleep 60
    
    # 监控关键服务
    if ! kill -0 $SIGNAL_PID 2>/dev/null; then
        echo "⚠️ 信号接收服务异常，尝试重启..."
        python3 signal_receiver.py > logs/signal_receiver.out.log 2> logs/signal_receiver.err.log &
        SIGNAL_PID=$!
    fi
    
    if ! kill -0 $MT5_TRADER_PID 2>/dev/null; then
        echo "⚠️ MT5交易服务异常，尝试重启..."
        python3 mt5_trader_linux.py > logs/mt5_trader.out.log 2> logs/mt5_trader.err.log &
        MT5_TRADER_PID=$!
    fi
    
    if ! kill -0 $WEB_PID 2>/dev/null; then
        echo "⚠️ Web界面服务异常，尝试重启..."
        python3 web_interface.py > logs/web_interface.out.log 2> logs/web_interface.err.log &
        WEB_PID=$!
    fi
done
