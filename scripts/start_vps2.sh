#!/bin/bash

# VPS2服务启动脚本
# 此脚本在MetaTrader5启动后启动VPS2的所有服务

echo "=== VPS2服务启动脚本 ==="

# 等待MetaTrader5和Wine环境完全启动
echo "等待MetaTrader5和Wine环境启动..."
sleep 30

# 检查MT5Linux服务器是否运行
echo "检查MT5Linux服务器状态..."
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if ss -tuln | grep ":8001" > /dev/null; then
        echo "✅ MT5Linux服务器已启动 (端口8001)"
        break
    else
        echo "⏳ 等待MT5Linux服务器启动... (尝试 $((attempt+1))/$max_attempts)"
        sleep 5
        attempt=$((attempt+1))
    fi
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ MT5Linux服务器启动超时，但继续启动VPS2服务"
fi

# 切换到VPS2目录
cd /vps2

# 初始化VPS2配置和数据库
echo "初始化VPS2配置..."
python3 /scripts/init_vps2_config.py
if [ $? -ne 0 ]; then
    echo "❌ VPS2初始化失败"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 设置Python路径
export PYTHONPATH="/vps2:$PYTHONPATH"

echo "🚀 启动VPS2服务..."

# 启动信号接收服务 (端口9999)
echo "启动信号接收服务 (端口9999)..."
python3 signal_receiver.py > logs/signal_receiver.out.log 2> logs/signal_receiver.err.log &
SIGNAL_PID=$!
echo "信号接收服务PID: $SIGNAL_PID"

# 等待信号接收服务启动
sleep 5

# 启动MT5交易模块
echo "启动MT5交易模块..."
python3 mt5_trader.py > logs/mt5_trader.out.log 2> logs/mt5_trader.err.log &
MT5_TRADER_PID=$!
echo "MT5交易模块PID: $MT5_TRADER_PID"

# 等待MT5交易模块启动
sleep 5

# 启动Web界面 (端口8080)
echo "启动VPS2 Web管理界面 (端口8080)..."
python3 web_interface.py > logs/web_interface.out.log 2> logs/web_interface.err.log &
WEB_PID=$!
echo "Web界面PID: $WEB_PID"

# 等待所有服务启动
sleep 10

# 检查服务状态
echo "=== 服务状态检查 ==="

# 检查信号接收服务
if ss -tuln | grep ":9999" > /dev/null; then
    echo "✅ 信号接收服务运行正常 (端口9999)"
else
    echo "❌ 信号接收服务启动失败"
fi

# 检查Web界面
if ss -tuln | grep ":8080" > /dev/null; then
    echo "✅ VPS2 Web界面运行正常 (端口8080)"
else
    echo "❌ VPS2 Web界面启动失败"
fi

# 检查进程是否存活
if kill -0 $SIGNAL_PID 2>/dev/null; then
    echo "✅ 信号接收进程运行正常"
else
    echo "❌ 信号接收进程异常"
fi

if kill -0 $MT5_TRADER_PID 2>/dev/null; then
    echo "✅ MT5交易进程运行正常"
else
    echo "❌ MT5交易进程异常"
fi

if kill -0 $WEB_PID 2>/dev/null; then
    echo "✅ Web界面进程运行正常"
else
    echo "❌ Web界面进程异常"
fi

echo "=== VPS2服务启动完成 ==="
echo "访问地址："
echo "- VPS2 Web管理: http://localhost:8080"
echo "- 信号接收API: http://localhost:9999"
echo "- MetaTrader5 VNC: http://localhost:3000"

# 保持脚本运行，监控服务状态
while true; do
    sleep 60
    
    # 检查关键服务是否还在运行
    if ! kill -0 $SIGNAL_PID 2>/dev/null; then
        echo "⚠️ 信号接收服务异常，尝试重启..."
        python3 signal_receiver.py > logs/signal_receiver.out.log 2> logs/signal_receiver.err.log &
        SIGNAL_PID=$!
    fi
    
    if ! kill -0 $MT5_TRADER_PID 2>/dev/null; then
        echo "⚠️ MT5交易服务异常，尝试重启..."
        python3 mt5_trader.py > logs/mt5_trader.out.log 2> logs/mt5_trader.err.log &
        MT5_TRADER_PID=$!
    fi
    
    if ! kill -0 $WEB_PID 2>/dev/null; then
        echo "⚠️ Web界面服务异常，尝试重启..."
        python3 web_interface.py > logs/web_interface.out.log 2> logs/web_interface.err.log &
        WEB_PID=$!
    fi
done
