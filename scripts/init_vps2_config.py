#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPS2配置初始化脚本 - 适配Docker环境
"""

import os
import json
import shutil

def init_vps2_config():
    """初始化VPS2配置文件"""
    
    # 检查是否已有配置文件
    config_path = "/vps2/config.json"
    docker_config_path = "/vps2/config_docker.json"
    
    if not os.path.exists(config_path):
        if os.path.exists(docker_config_path):
            print("复制Docker环境配置文件...")
            shutil.copy2(docker_config_path, config_path)
            print("✅ 配置文件初始化完成")
        else:
            print("❌ 找不到配置文件模板")
            return False
    else:
        print("✅ 配置文件已存在")
    
    # 读取并验证配置
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保MT5路径正确
        mt5_path = "/config/.wine/drive_c/Program Files/MetaTrader 5/terminal64.exe"
        if config.get('mt5_path') != mt5_path:
            config['mt5_path'] = mt5_path
            
            # 保存更新的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            print("✅ MT5路径已更新为Docker环境路径")
        
        print("✅ 配置验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def create_database():
    """创建VPS2数据库"""
    import sqlite3
    
    db_path = "/vps2/trading_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建信号表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                trading_pair TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                close_price REAL,
                processed BOOLEAN DEFAULT FALSE,
                order_ticket INTEGER,
                order_result TEXT,
                webhook_data TEXT,
                process_status TEXT DEFAULT 'pending',
                failure_reason TEXT
            )
        ''')
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_id INTEGER,
                ticket INTEGER,
                symbol TEXT,
                type TEXT,
                volume REAL,
                price REAL,
                sl REAL,
                tp REAL,
                timestamp TEXT,
                status TEXT,
                profit REAL,
                FOREIGN KEY (signal_id) REFERENCES signals (id)
            )
        ''')
        
        # 创建登录安全表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ip_address TEXT NOT NULL,
                username TEXT,
                success BOOLEAN NOT NULL,
                timestamp TEXT NOT NULL,
                user_agent TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

if __name__ == "__main__":
    print("=== VPS2 Docker环境初始化 ===")
    
    # 创建日志目录
    os.makedirs("/vps2/logs", exist_ok=True)
    print("✅ 日志目录创建完成")
    
    # 初始化配置
    if init_vps2_config():
        print("✅ 配置初始化成功")
    else:
        print("❌ 配置初始化失败")
        exit(1)
    
    # 初始化数据库
    if create_database():
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")
        exit(1)
    
    print("=== VPS2初始化完成 ===")
