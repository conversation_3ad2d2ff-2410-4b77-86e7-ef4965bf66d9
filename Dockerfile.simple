FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV WINEPREFIX="/config/.wine"
ENV DISPLAY=:99

# 更新系统并安装基础包
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    wget \
    curl \
    sqlite3 \
    xvfb \
    x11vnc \
    fluxbox \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建虚拟环境并安装Python包
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 安装Python依赖
RUN pip install --no-cache-dir \
    flask \
    werkzeug \
    requests \
    ntplib \
    pytz \
    Pillow \
    rpyc

# 复制VPS2项目文件
COPY /vps2 /vps2
WORKDIR /vps2

# 创建日志目录
RUN mkdir -p /vps2/logs

# 复制启动脚本
COPY /scripts /scripts
RUN chmod +x /scripts/*.sh /scripts/*.py

# 暴露端口
EXPOSE 5900 8080 9999

# 创建简化的启动脚本
RUN echo '#!/bin/bash\n\
echo "=== VPS2测试系统启动 ==="\n\
\n\
# 启动虚拟显示\n\
Xvfb :99 -screen 0 1024x768x24 &\n\
sleep 2\n\
\n\
# 启动窗口管理器\n\
fluxbox &\n\
sleep 2\n\
\n\
# 启动VNC服务器\n\
x11vnc -display :99 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever -shared &\n\
sleep 2\n\
\n\
cd /vps2\n\
\n\
# 初始化配置\n\
python3 /scripts/init_vps2_config.py\n\
\n\
echo "启动VPS2服务..."\n\
\n\
# 启动信号接收服务\n\
python3 signal_receiver.py > logs/signal_receiver.out.log 2> logs/signal_receiver.err.log &\n\
SIGNAL_PID=$!\n\
echo "信号接收服务PID: $SIGNAL_PID"\n\
\n\
sleep 5\n\
\n\
# 启动Web界面\n\
python3 web_interface.py > logs/web_interface.out.log 2> logs/web_interface.err.log &\n\
WEB_PID=$!\n\
echo "Web界面PID: $WEB_PID"\n\
\n\
sleep 10\n\
\n\
echo "=== 服务状态检查 ==="\n\
\n\
if ss -tuln | grep ":9999" > /dev/null; then\n\
    echo "✅ 信号接收服务运行正常 (端口9999)"\n\
else\n\
    echo "❌ 信号接收服务启动失败"\n\
fi\n\
\n\
if ss -tuln | grep ":8080" > /dev/null; then\n\
    echo "✅ VPS2 Web界面运行正常 (端口8080)"\n\
else\n\
    echo "❌ VPS2 Web界面启动失败"\n\
fi\n\
\n\
if ss -tuln | grep ":5900" > /dev/null; then\n\
    echo "✅ VNC服务器运行正常 (端口5900)"\n\
else\n\
    echo "❌ VNC服务器启动失败"\n\
fi\n\
\n\
echo "=== VPS2测试系统启动完成 ==="\n\
echo "访问地址："\n\
echo "- VPS2 Web管理: http://localhost:8080"\n\
echo "- 信号接收API: http://localhost:9999"\n\
echo "- VNC: localhost:5900"\n\
\n\
# 保持运行\n\
tail -f /dev/null' > /start_simple.sh && \
chmod +x /start_simple.sh

CMD ["/start_simple.sh"]
