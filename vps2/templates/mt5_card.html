<!-- This is a reusable template for showing MT5 position data -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">MT5 实时持仓数据</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-3">
                    <div class="card-body">
                        <h6 class="card-title text-primary">价格信息</h6>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">开仓价格</div>
                            <div class="col-6">{{ position.price_open }}</div>
                        </div>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">当前价格</div>
                            <div class="col-6">{{ position.price_current }}</div>
                        </div>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">价格变化</div>
                            <div class="col-6 {% if (position.price_current - position.price_open > 0 and position.type_str == 'buy') or (position.price_current - position.price_open < 0 and position.type_str == 'sell') %}text-success{% elif position.price_current - position.price_open != 0 %}text-danger{% endif %}">
                                {{ "%.5f"|format(position.price_current - position.price_open) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-3">
                    <div class="card-body">
                        <h6 class="card-title text-primary">盈亏状况</h6>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">当前盈亏</div>
                            <div class="col-6 {% if position.profit > 0 %}text-success{% elif position.profit < 0 %}text-danger{% endif %}">
                                {{ "%.2f"|format(position.profit) }}
                            </div>
                        </div>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">隔夜利息</div>
                            <div class="col-6 {% if position.swap > 0 %}text-success{% elif position.swap < 0 %}text-danger{% endif %}">
                                {{ "%.2f"|format(position.swap) }}
                            </div>
                        </div>
                        <div class="row detail-row">
                            <div class="col-6 detail-label">总计</div>
                            <div class="col-6 {% if position.profit + position.swap > 0 %}text-success{% elif position.profit + position.swap < 0 %}text-danger{% endif %}">
                                {{ "%.2f"|format(position.profit + position.swap) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
