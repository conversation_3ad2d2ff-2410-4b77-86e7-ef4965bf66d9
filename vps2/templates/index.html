<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>MT5交易系统 - 控制面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 0.75rem;
            --border-radius-sm: 0.5rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 现代化导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--dark-color) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand::before {
            content: "📊";
            font-size: 1.25rem;
        }

        .nav-link {
            font-weight: 500;
            color: var(--secondary-color) !important;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
            position: relative;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--primary-color) !important;
            background-color: rgba(37, 99, 235, 0.1);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1rem;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background-color: var(--primary-color);
            border-radius: 50%;
        }

        /* 主内容区域 */
        .main-content {
            background: var(--light-color);
            min-height: calc(100vh - 80px);
            border-radius: 2rem 2rem 0 0;
            margin-top: 1rem;
            padding: 2rem 1rem;
            position: relative;
        }

        /* 现代化卡片设计 */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            justify-content: between;
        }

        /* 状态徽章优化 */
        .badge {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), #059669) !important;
            color: white;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
            color: white;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
            color: white;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, var(--info-color), #0891b2) !important;
            color: white;
        }

        /* 统计卡片 */
        .stats-card {
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* 按钮优化 */
        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            padding: 0.625rem 1.25rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
        }

        .btn-outline-secondary {
            border: 1px solid var(--border-color);
            color: var(--secondary-color);
            background: white;
        }

        .btn-outline-secondary:hover {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        /* 动画效果 */
        @keyframes price-flash {
            0% { background-color: transparent; }
            50% { background-color: rgba(37, 99, 235, 0.1); }
            100% { background-color: transparent; }
        }

        .price-updated {
            animation: price-flash 1s ease;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .rotate-animation {
            animation: rotate 1s linear;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }

        /* 价格卡片特殊样式 */
        .price-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .price-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .price-card:hover::before {
            opacity: 1;
        }

        .price-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
        }

        /* 价格数值样式 */
        .price-value {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 700;
            letter-spacing: -0.025em;
            font-size: 1.5rem !important;
            line-height: 1.2;
            color: var(--dark-color);
        }

        .price-symbol {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.25rem;
        }

        .price-spread {
            font-size: 0.75rem;
            color: var(--secondary-color);
            font-weight: 500;
        }

        .price-icon-wrapper {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            margin-bottom: 0.75rem;
        }

        .price-bid {
            color: var(--danger-color) !important;
        }

        .price-ask {
            color: var(--success-color) !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0.75rem;
                margin-top: 0.5rem;
                border-radius: 1rem 1rem 0 0;
            }

            .card {
                margin-bottom: 1rem;
            }

            .card-header {
                padding: 1rem;
                font-size: 0.95rem;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .badge {
                padding: 0.375rem 0.75rem;
                font-size: 0.8rem;
            }

            .navbar-brand {
                font-size: 1.25rem;
            }

            /* 移动端表格优化 */
            .table-responsive {
                border-radius: var(--border-radius-sm);
                box-shadow: var(--shadow-sm);
            }

            .table {
                font-size: 0.875rem;
            }

            .table th, .table td {
                padding: 0.75rem 0.5rem;
                vertical-align: middle;
            }

            /* 移动端价格卡片优化 */
            .price-card {
                margin-bottom: 0.5rem;
            }

            .price-card .p-4 {
                padding: 1.5rem !important;
            }

            .price-value {
                font-size: 1.25rem !important;
            }

            .price-icon-wrapper {
                width: 35px;
                height: 35px;
                margin-bottom: 0.5rem;
            }

            .price-symbol {
                font-size: 1rem;
            }

            .price-spread {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 0.75rem 0.5rem;
            }

            .card-header {
                padding: 0.875rem;
                flex-direction: column;
                gap: 0.5rem;
                align-items: flex-start;
            }

            .btn-toolbar {
                flex-direction: column;
                gap: 0.5rem;
                width: 100%;
            }

            .btn-toolbar .btn {
                width: 100%;
                justify-content: center;
            }

            /* 移动端状态徽章堆叠 */
            .status-badges {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                width: 100%;
            }

            .status-badges .badge {
                width: 100%;
                justify-content: center;
            }

            /* 超小屏幕价格卡片优化 */
            .price-card .p-4 {
                padding: 1rem !important;
            }

            .price-value {
                font-size: 1.125rem !important;
            }

            .price-icon-wrapper {
                width: 30px;
                height: 30px;
                margin-bottom: 0.375rem;
            }

            .price-symbol {
                font-size: 0.9rem;
                margin-bottom: 0.125rem;
            }
        }

        /* 触控优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn, .nav-link, .card {
                -webkit-tap-highlight-color: transparent;
            }

            .btn:active {
                transform: scale(0.98);
            }

            .stats-card:active {
                transform: scale(0.98);
            }
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .loading::after {
            content: '';
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: rotate 1s linear infinite;
        }

        /* 页面标题 */
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-subtitle {
            color: var(--secondary-color);
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        /* 工具栏优化 */
        .toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .toolbar > * {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 现代化导航栏 -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="bi bi-list fs-4"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('index') }}">
                            <i class="bi bi-speedometer2"></i> 控制面板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('signals') }}">
                            <i class="bi bi-broadcast"></i> 信号管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('orders') }}">
                            <i class="bi bi-list-check"></i> 订单管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="bi bi-graph-up"></i> 交易报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="bi bi-gear"></i> 系统设置
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right"></i> 退出登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content" style="margin-top: 80px;">
        <div class="container-fluid">
            <!-- 页面标题和工具栏 -->
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h1 class="page-title">
                        <i class="bi bi-speedometer2"></i>
                        控制面板
                    </h1>
                    <p class="page-subtitle">实时监控您的交易系统状态</p>
                </div>

                <!-- 紧急操作按钮 -->
                <div class="d-flex flex-column flex-md-row gap-2">
                    <button id="closeAllPositionsBtn" class="btn btn-danger">
                        <i class="bi bi-x-circle-fill"></i>
                        <span class="d-none d-md-inline">平仓所有持仓</span>
                        <span class="d-md-none">平仓</span>
                    </button>
                    <button id="closeProfitablePositionsBtn" class="btn btn-success">
                        <i class="bi bi-check-circle-fill"></i>
                        <span class="d-none d-md-inline">平仓盈利订单</span>
                        <span class="d-md-none">盈利</span>
                    </button>
                </div>
            </div>

            <!-- 系统状态卡片 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="bi bi-activity"></i> 系统状态
                        </div>
                        <div class="card-body">
                            <div class="status-badges d-flex flex-wrap gap-2">
                                <span class="badge {% if trading_enabled %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if trading_enabled %}
                                        <i class="bi bi-play-fill"></i> 交易已启用
                                    {% else %}
                                        <i class="bi bi-pause-fill"></i> 交易已暂停
                                    {% endif %}
                                </span>
                                <span class="badge {% if mt5_connected %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if mt5_connected %}
                                        <i class="bi bi-check-circle-fill"></i> MT5已连接
                                    {% else %}
                                        <i class="bi bi-x-circle-fill"></i> MT5未连接
                                    {% endif %}
                                </span>
                                <span class="badge {% if signal_receiver_running %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if signal_receiver_running %}
                                        <i class="bi bi-broadcast"></i> 信号接收服务运行中
                                    {% else %}
                                        <i class="bi bi-broadcast-off"></i> 信号接收服务未运行
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息提示 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="row mb-4">
                        <div class="col-12">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show border-0 shadow-sm" role="alert">
                                    <div class="d-flex align-items-center">
                                        {% if category == 'success' %}
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        {% elif category == 'danger' %}
                                            <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                                        {% elif category == 'warning' %}
                                            <i class="bi bi-exclamation-circle-fill text-warning me-2"></i>
                                        {% else %}
                                            <i class="bi bi-info-circle-fill text-info me-2"></i>
                                        {% endif %}
                                        <div class="flex-grow-1">{{ message }}</div>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            {% endwith %}

            <!-- 账户信息卡片 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card fade-in-up">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-wallet2 me-2"></i>
                                <span>账户信息</span>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm" id="refreshAccountBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span class="d-none d-md-inline ms-1">刷新</span>
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <div class="row g-0">
                                <!-- 账户余额 -->
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-4 border-end border-bottom border-lg-bottom-0">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <div class="text-muted small text-uppercase fw-semibold mb-1">账户余额</div>
                                                <div class="h4 mb-0 fw-bold" id="account-balance">
                                                    {{ "%.2f"|format(account_info['balance']) }} {{ account_info['currency'] }}
                                                </div>
                                            </div>
                                            <div class="text-primary">
                                                <i class="bi bi-cash fs-2"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 账户净值 -->
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-4 border-end border-bottom border-md-bottom-0">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <div class="text-muted small text-uppercase fw-semibold mb-1">账户净值</div>
                                                <div class="h4 mb-0 fw-bold" id="account-equity">
                                                    {{ "%.2f"|format(account_info['equity']) }} {{ account_info['currency'] }}
                                                </div>
                                            </div>
                                            <div class="text-success">
                                                <i class="bi bi-graph-up fs-2"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 浮动盈亏 -->
                                <div class="col-lg-3 col-md-6 col-12" id="profit-card">
                                    <div class="p-4 border-end border-bottom border-lg-bottom-0">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <div class="text-muted small text-uppercase fw-semibold mb-1" id="profit-label">浮动盈亏</div>
                                                <div class="h4 mb-0 fw-bold {% if account_info['profit'] >= 0 %}text-success{% else %}text-danger{% endif %}" id="account-profit">
                                                    {{ "%.2f"|format(account_info['profit']) }} {{ account_info['currency'] }}
                                                </div>
                                            </div>
                                            <div class="{% if account_info['profit'] >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                <i class="bi {% if account_info['profit'] >= 0 %}bi-graph-up-arrow{% else %}bi-graph-down-arrow{% endif %} fs-2" id="profit-icon"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 可用保证金 -->
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-4">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <div class="text-muted small text-uppercase fw-semibold mb-1">可用保证金</div>
                                                <div class="h4 mb-0 fw-bold" id="account-free-margin">
                                                    {{ "%.2f"|format(account_info['free_margin']) }} {{ account_info['currency'] }}
                                                </div>
                                            </div>
                                            <div class="text-info">
                                                <i class="bi bi-shield-check fs-2"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时价格卡片 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card fade-in-up">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-graph-up-arrow me-2"></i>
                                <span>实时价格</span>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <small class="text-muted d-none d-md-inline">
                                    更新时间: <span id="price-update-time">-</span>
                                </small>
                                <button class="btn btn-outline-secondary btn-sm" id="refreshPricesBtn">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    <span class="d-none d-md-inline ms-1">刷新</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <!-- 第一行：主要交易品种 -->
                            <div class="row g-0">
                                <!-- XAUUSD -->
                                <div class="col-xl-3 col-lg-4 col-md-6 col-12" id="price-XAUUSD">
                                    <div class="p-4 border-end border-bottom position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-warning bg-opacity-10 mx-auto">
                                                <i class="bi bi-gem text-warning fs-5"></i>
                                            </div>
                                            <div class="price-symbol">XAUUSD</div>
                                            <div class="price-value mb-2" id="price-value-XAUUSD">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-XAUUSD">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- BTCUSD -->
                                <div class="col-xl-3 col-lg-4 col-md-6 col-12" id="price-BTCUSD">
                                    <div class="p-4 border-end border-bottom position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-warning bg-opacity-10 mx-auto">
                                                <i class="bi bi-currency-bitcoin text-warning fs-5"></i>
                                            </div>
                                            <div class="price-symbol">BTCUSD</div>
                                            <div class="price-value mb-2" id="price-value-BTCUSD">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-BTCUSD">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ETHUSD -->
                                <div class="col-xl-3 col-lg-4 col-md-6 col-12" id="price-ETHUSD">
                                    <div class="p-4 border-end border-bottom position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-primary bg-opacity-10 mx-auto">
                                                <i class="bi bi-currency-exchange text-primary fs-5"></i>
                                            </div>
                                            <div class="price-symbol">ETHUSD</div>
                                            <div class="price-value mb-2" id="price-value-ETHUSD">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-ETHUSD">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- GBPJPY -->
                                <div class="col-xl-3 col-lg-4 col-md-6 col-12" id="price-GBPJPY">
                                    <div class="p-4 border-bottom position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-success bg-opacity-10 mx-auto">
                                                <i class="bi bi-currency-pound text-success fs-5"></i>
                                            </div>
                                            <div class="price-symbol">GBPJPY</div>
                                            <div class="price-value mb-2" id="price-value-GBPJPY">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-GBPJPY">点差: --</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：其他交易品种 -->
                            <div class="row g-0">
                                <!-- BRENT -->
                                <div class="col-xl-3 col-lg-3 col-md-6 col-12" id="price-BRENT">
                                    <div class="p-4 border-end border-bottom border-xl-bottom-0 position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-dark bg-opacity-10 mx-auto">
                                                <i class="bi bi-droplet-fill text-dark fs-5"></i>
                                            </div>
                                            <div class="price-symbol">BRENT</div>
                                            <div class="price-value mb-2" id="price-value-BRENT">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-BRENT">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- XTIUSD -->
                                <div class="col-xl-3 col-lg-3 col-md-6 col-12" id="price-XTIUSD">
                                    <div class="p-4 border-end border-bottom border-xl-bottom-0 position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-info bg-opacity-10 mx-auto">
                                                <i class="bi bi-droplet-half text-info fs-5"></i>
                                            </div>
                                            <div class="price-symbol">XTIUSD</div>
                                            <div class="price-value mb-2" id="price-value-XTIUSD">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-XTIUSD">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- GBPUSD -->
                                <div class="col-xl-3 col-lg-3 col-md-6 col-12" id="price-GBPUSD">
                                    <div class="p-4 border-end border-bottom border-xl-bottom-0 position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-secondary bg-opacity-10 mx-auto">
                                                <i class="bi bi-currency-dollar text-secondary fs-5"></i>
                                            </div>
                                            <div class="price-symbol">GBPUSD</div>
                                            <div class="price-value mb-2" id="price-value-GBPUSD">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-GBPUSD">点差: --</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- DXY -->
                                <div class="col-xl-3 col-lg-3 col-md-6 col-12" id="price-DXY">
                                    <div class="p-4 position-relative price-card">
                                        <div class="text-center">
                                            <div class="price-icon-wrapper bg-primary bg-opacity-10 mx-auto">
                                                <i class="bi bi-graph-up text-primary fs-5"></i>
                                            </div>
                                            <div class="price-symbol">DXY</div>
                                            <div class="price-value mb-2" id="price-value-DXY">
                                                <span class="loading">加载中</span>
                                            </div>
                                            <div class="price-spread" id="price-spread-DXY">点差: --</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 价格更新信息 -->
                            <div class="border-top p-3 bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="bi bi-clock me-1"></i>
                                        5秒自动更新
                                    </small>
                                    <small class="text-muted d-md-none">
                                        更新: <span id="price-update-time-mobile">-</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stats-card fade-in-up h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small text-uppercase fw-semibold mb-1">总信号</div>
                                    <div class="h3 mb-0 fw-bold">{{ signal_stats['total_signals'] }}</div>
                                    <div class="small text-success mt-1">
                                        <i class="bi bi-arrow-up"></i> 实时监控
                                    </div>
                                </div>
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-broadcast fs-4 text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stats-card fade-in-up h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small text-uppercase fw-semibold mb-1">已执行交易</div>
                                    <div class="h3 mb-0 fw-bold">{{ order_stats['total_orders'] }}</div>
                                    <div class="small text-info mt-1">
                                        <i class="bi bi-check-circle"></i> 总计
                                    </div>
                                </div>
                                <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-graph-up fs-4 text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stats-card fade-in-up h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small text-uppercase fw-semibold mb-1">活跃订单</div>
                                    <div class="h3 mb-0 fw-bold">{{ order_stats['active_orders'] }}</div>
                                    <div class="small text-warning mt-1">
                                        <i class="bi bi-clock"></i> 持仓中
                                    </div>
                                </div>
                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-clock-history fs-4 text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stats-card fade-in-up h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small text-uppercase fw-semibold mb-1">总盈亏</div>
                                    <div class="h3 mb-0 fw-bold {% if order_stats['total_profit'] > 0 %}text-success{% elif order_stats['total_profit'] < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {{ "%.2f"|format(order_stats['total_profit']|float) }}
                                    </div>
                                    <div class="small {% if order_stats['total_profit'] > 0 %}text-success{% elif order_stats['total_profit'] < 0 %}text-danger{% else %}text-muted{% endif %} mt-1">
                                        <i class="bi {% if order_stats['total_profit'] > 0 %}bi-arrow-up{% elif order_stats['total_profit'] < 0 %}bi-arrow-down{% else %}bi-dash{% endif %}"></i>
                                        {% if order_stats['total_profit'] > 0 %}盈利{% elif order_stats['total_profit'] < 0 %}亏损{% else %}持平{% endif %}
                                    </div>
                                </div>
                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-cash fs-4 text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活跃订单 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card fade-in-up">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-list-check me-2"></i>
                                <span>活跃订单</span>
                                <span class="badge bg-primary ms-2" id="active-orders-count">{{ active_orders|length }}</span>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-secondary btn-sm" id="refreshActiveOrdersBtn">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    <span class="d-none d-md-inline ms-1">刷新</span>
                                </button>
                                <a href="{{ url_for('orders', status='open') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-arrow-right"></i>
                                    <span class="d-none d-md-inline ms-1">查看全部</span>
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0" id="active-orders-container">
                            {% if active_orders %}
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0" id="active-orders-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th scope="col" class="border-0 fw-semibold">订单号</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">交易对</th>
                                                <th scope="col" class="border-0 fw-semibold">方向</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">交易量</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">开仓价</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">止损/止盈</th>
                                                <th scope="col" class="border-0 fw-semibold">当前盈亏</th>
                                                <th scope="col" class="border-0 fw-semibold">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="active-orders-tbody">
                                            {% for order in active_orders %}
                                                <tr class="align-middle">
                                                    <td class="fw-bold">{{ order['ticket'] }}</td>
                                                    <td class="d-none d-md-table-cell">
                                                        <span class="fw-semibold">{{ order['trading_pair'] }}</span>
                                                    </td>
                                                    <td>
                                                        {% if order['operation'] == 'buy' %}
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-arrow-up"></i> 买入
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-danger">
                                                                <i class="bi bi-arrow-down"></i> 卖出
                                                            </span>
                                                        {% endif %}
                                                        <div class="d-md-none small text-muted mt-1">{{ order['trading_pair'] }}</div>
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">{{ order['volume'] }}</td>
                                                    <td class="d-none d-md-table-cell">{{ order['price'] }}</td>
                                                    <td class="d-none d-lg-table-cell">
                                                        <div class="small">
                                                            <div>SL: {{ order['sl'] if order['sl'] else '-' }}</div>
                                                            <div>TP: {{ order['tp'] if order['tp'] else '-' }}</div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="fw-bold {% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% else %}text-muted{% endif %}">
                                                            {{ "%.2f"|format(order['profit']|float) }}
                                                        </span>
                                                        <div class="d-md-none small text-muted">
                                                            Vol: {{ order['volume'] }} | Price: {{ order['price'] }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            {% if order['id'] %}
                                                                <a href="{{ url_for('order_detail', order_id=order['id']) }}"
                                                                   class="btn btn-outline-primary btn-sm">
                                                                    <i class="bi bi-eye"></i>
                                                                    <span class="d-none d-lg-inline ms-1">详情</span>
                                                                </a>
                                                            {% else %}
                                                                <a href="{{ url_for('position_detail', ticket=order['ticket']) }}"
                                                                   class="btn btn-outline-primary btn-sm">
                                                                    <i class="bi bi-eye"></i>
                                                                    <span class="d-none d-lg-inline ms-1">详情</span>
                                                                </a>
                                                            {% endif %}
                                                            <button class="btn btn-outline-danger btn-sm close-position-btn"
                                                                    data-ticket="{{ order['ticket'] }}"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#closePositionModal">
                                                                <i class="bi bi-x-circle"></i>
                                                                <span class="d-none d-lg-inline ms-1">平仓</span>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5" id="no-active-orders">
                                    <div class="mb-3">
                                        <i class="bi bi-inbox fs-1 text-muted"></i>
                                    </div>
                                    <h5 class="text-muted">暂无活跃订单</h5>
                                    <p class="text-muted mb-0">当前没有正在进行的交易</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近信号 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card fade-in-up">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-broadcast me-2"></i>
                                <span>最近信号</span>
                                <span class="badge bg-info ms-2">{{ signals|length }}</span>
                            </div>
                            <a href="{{ url_for('signals') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-arrow-right"></i>
                                <span class="d-none d-md-inline ms-1">查看全部</span>
                            </a>
                        </div>
                        <div class="card-body p-0">
                            {% if signals %}
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th scope="col" class="border-0 fw-semibold">ID</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">时间</th>
                                                <th scope="col" class="border-0 fw-semibold">交易对</th>
                                                <th scope="col" class="border-0 fw-semibold">信号类型</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">信号价格</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">实际成交价</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">状态</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">订单号</th>
                                                <th scope="col" class="border-0 fw-semibold">结果</th>
                                                <th scope="col" class="border-0 fw-semibold">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for signal in signals %}
                                                <tr class="align-middle">
                                                    <td class="fw-bold">#{{ signal['id'] }}</td>
                                                    <td class="d-none d-md-table-cell">
                                                        <div class="small">{{ signal['timestamp'].replace('T', ' ').split('.')[0] }}</div>
                                                    </td>
                                                    <td>
                                                        <span class="fw-semibold">{{ signal['trading_pair'] }}</span>
                                                        <div class="d-md-none small text-muted">
                                                            {{ signal['timestamp'].replace('T', ' ').split('.')[0] }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {% if signal['signal_type'] == 'buy' %}
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-arrow-up"></i> 买入
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-danger">
                                                                <i class="bi bi-arrow-down"></i> 卖出
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">
                                                        {% if signal['signal_price'] %}
                                                            <span class="fw-semibold">{{ "%.4f"|format(signal['signal_price']|float) }}</span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">
                                                        {% if signal['order_price'] and signal['order_result'] == '成功' %}
                                                            <span class="fw-semibold text-success">{{ "%.4f"|format(signal['order_price']|float) }}</span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="d-none d-md-table-cell">
                                                        {% if signal['processed'] == 1 %}
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-check-circle"></i> 已处理
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-warning">
                                                                <i class="bi bi-clock"></i> 未处理
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="d-none d-md-table-cell">
                                                        {% if signal['order_ticket'] %}
                                                            <span class="fw-bold">{{ signal['order_ticket'] }}</span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if signal['order_result'] %}
                                                            {% if signal['order_result'] == '成功' %}
                                                                <span class="badge bg-success">
                                                                    <i class="bi bi-check"></i> 成功
                                                                </span>
                                                            {% else %}
                                                                <span class="badge bg-danger">
                                                                    <i class="bi bi-x"></i> 失败
                                                                </span>
                                                            {% endif %}
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                        <div class="d-lg-none small text-muted mt-1">
                                                            {% if signal['processed'] == 1 %}已处理{% else %}未处理{% endif %}
                                                            {% if signal['order_ticket'] %} | #{{ signal['order_ticket'] }}{% endif %}
                                                        </div>
                                                        <div class="d-lg-none small text-muted mt-1">
                                                            {% if signal['signal_price'] %}信号价: {{ "%.4f"|format(signal['signal_price']|float) }}{% endif %}
                                                            {% if signal['order_price'] and signal['order_result'] == '成功' %} | 成交价: {{ "%.4f"|format(signal['order_price']|float) }}{% endif %}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="{{ url_for('signal_detail', signal_id=signal['id']) }}"
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="bi bi-eye"></i>
                                                            <span class="d-none d-lg-inline ms-1">详情</span>
                                                        </a>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="bi bi-broadcast-pin fs-1 text-muted"></i>
                                    </div>
                                    <h5 class="text-muted">暂无信号数据</h5>
                                    <p class="text-muted mb-0">等待交易信号接收</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近已平仓订单 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card fade-in-up">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle me-2"></i>
                                <span>最近已平仓订单</span>
                                <small class="text-muted ms-2">(最近1天)</small>
                                <span class="badge bg-secondary ms-2">{{ closed_orders|length }}</span>
                            </div>
                            <a href="{{ url_for('orders', status='closed') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-arrow-right"></i>
                                <span class="d-none d-md-inline ms-1">查看全部</span>
                            </a>
                        </div>
                        <div class="card-body p-0">
                            {% if closed_orders %}
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th scope="col" class="border-0 fw-semibold">订单号</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">交易对</th>
                                                <th scope="col" class="border-0 fw-semibold">方向</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">开仓价</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">平仓价</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">交易量</th>
                                                <th scope="col" class="border-0 fw-semibold">盈亏</th>
                                                <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">平仓时间</th>
                                                <th scope="col" class="border-0 fw-semibold">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for order in closed_orders %}
                                                <tr class="align-middle">
                                                    <td class="fw-bold">{{ order['ticket'] }}</td>
                                                    <td class="d-none d-md-table-cell">
                                                        <span class="fw-semibold">{{ order['trading_pair'] }}</span>
                                                    </td>
                                                    <td>
                                                        {% if order['operation'] == 'buy' %}
                                                            <span class="badge bg-success">
                                                                <i class="bi bi-arrow-up"></i> 买入
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-danger">
                                                                <i class="bi bi-arrow-down"></i> 卖出
                                                            </span>
                                                        {% endif %}
                                                        <div class="d-md-none small text-muted mt-1">{{ order['trading_pair'] }}</div>
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">{{ order['price'] }}</td>
                                                    <td class="d-none d-lg-table-cell">{{ order['close_price'] }}</td>
                                                    <td class="d-none d-md-table-cell">{{ order['volume'] }}</td>
                                                    <td>
                                                        <span class="fw-bold {% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% else %}text-muted{% endif %}">
                                                            {% if order['profit'] > 0 %}
                                                                <i class="bi bi-arrow-up"></i>
                                                            {% elif order['profit'] < 0 %}
                                                                <i class="bi bi-arrow-down"></i>
                                                            {% endif %}
                                                            {{ "%.2f"|format(order['profit']|float) }}
                                                        </span>
                                                        <div class="d-md-none small text-muted">
                                                            Vol: {{ order['volume'] }}
                                                        </div>
                                                        <div class="d-lg-none small text-muted">
                                                            {{ order['price'] }} → {{ order['close_price'] }}
                                                        </div>
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">
                                                        <div class="small">{{ order['close_time'].replace('T', ' ').split('.')[0] if order['close_time'] else '-' }}</div>
                                                    </td>
                                                    <td>
                                                        {% if order['id'] %}
                                                            <a href="{{ url_for('order_detail', order_id=order['id']) }}"
                                                               class="btn btn-outline-primary btn-sm">
                                                                <i class="bi bi-eye"></i>
                                                                <span class="d-none d-lg-inline ms-1">详情</span>
                                                            </a>
                                                        {% else %}
                                                            <a href="{{ url_for('position_detail', ticket=order['ticket']) }}"
                                                               class="btn btn-outline-primary btn-sm">
                                                                <i class="bi bi-eye"></i>
                                                                <span class="d-none d-lg-inline ms-1">详情</span>
                                                            </a>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="bi bi-archive fs-1 text-muted"></i>
                                    </div>
                                    <h5 class="text-muted">暂无已平仓订单</h5>
                                    <p class="text-muted mb-0">还没有完成的交易记录</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 平仓确认弹窗 -->
    <div class="modal fade" id="closePositionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认平仓</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要平仓订单 <span id="closePositionTicket" class="fw-bold"></span> 吗？</p>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="partialCloseCheck">
                        <label class="form-check-label" for="partialCloseCheck">
                            部分平仓
                        </label>
                    </div>
                    <div id="partialVolumeGroup" class="mb-3" style="display: none;">
                        <label for="partialVolume" class="form-label">平仓量</label>
                        <input type="number" class="form-control" id="partialVolume" step="0.01" min="0.01">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmCloseBtn">确认平仓</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        $(document).ready(function() {
            // 页面加载动画
            $('.fade-in-up').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });

            // 平仓按钮点击事件
            $('.close-position-btn').click(function() {
                const ticket = $(this).data('ticket');
                $('#closePositionTicket').text(ticket);
            });

            // 部分平仓选项
            $('#partialCloseCheck').change(function() {
                if($(this).is(':checked')) {
                    $('#partialVolumeGroup').show();
                } else {
                    $('#partialVolumeGroup').hide();
                }
            });

            // 账户信息刷新按钮
            $('#refreshAccountBtn').click(function() {
                const $btn = $(this);
                const $icon = $btn.find('i');

                // 添加加载状态
                $btn.prop('disabled', true);
                $icon.addClass('rotate-animation');

                $.ajax({
                    url: '/api/account_info',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if(data.success) {
                            // 刷新页面
                            location.reload();
                        } else {
                            showToast('获取账户信息失败: ' + data.error, 'error');
                        }
                    },
                    error: function() {
                        showToast('网络错误，请稍后再试', 'error');
                    },
                    complete: function() {
                        $btn.prop('disabled', false);
                        $icon.removeClass('rotate-animation');
                    }
                });
            });

            // Toast 通知函数
            function showToast(message, type = 'info') {
                const toastHtml = `
                    <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="bi ${type === 'error' ? 'bi-exclamation-triangle' : type === 'success' ? 'bi-check-circle' : 'bi-info-circle'} me-2"></i>
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `;

                // 创建toast容器（如果不存在）
                if (!$('#toast-container').length) {
                    $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
                }

                const $toast = $(toastHtml);
                $('#toast-container').append($toast);

                const toast = new bootstrap.Toast($toast[0]);
                toast.show();

                // 自动移除
                $toast.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            // 实时价格更新相关代码
            let priceUpdateInterval;

            // 格式化价格显示
            function formatPrice(price, symbol) {
                if (symbol === 'XAUUSD' || symbol === 'BRENT' || symbol === 'XTIUSD') {
                    return price.toFixed(2);  // 黄金和原油显示2位小数
                } else if (symbol === 'BTCUSD' || symbol === 'ETHUSD') {
                    return price.toFixed(1);  // 数字货币显示1位小数
                } else if (symbol === 'GBPJPY' || symbol === 'DXY') {
                    return price.toFixed(3);  // 英镑日元和美元指数显示3位小数
                } else if (symbol === 'GBPUSD') {
                    return price.toFixed(5);  // 英镑美元显示5位小数
                } else {
                    return price.toFixed(3);  // 其他货币对显示3位小数
                }
            }

            // 更新价格显示
            function updatePriceDisplay(data) {
                if (data.status === 'success') {
                    const prices = data.data;
                    for (let symbol in prices) {
                        const price = prices[symbol];
                        if (price.error) {
                            $(`#price-value-${symbol}`).html(`<span class="text-danger small">获取失败</span>`);
                            $(`#price-spread-${symbol}`).text(`错误: ${price.error}`);
                        } else {
                            const bidFormatted = formatPrice(price.bid, symbol);
                            const askFormatted = formatPrice(price.ask, symbol);

                            $(`#price-value-${symbol}`).html(`
                                <div class="d-flex justify-content-between">
                                    <span class="price-bid">${bidFormatted}</span>
                                    <span class="price-ask">${askFormatted}</span>
                                </div>
                            `);
                            $(`#price-spread-${symbol}`).text(`点差: ${price.spread}`);

                            // 添加闪烁效果
                            $(`#price-${symbol}`).addClass('price-updated');
                            setTimeout(() => {
                                $(`#price-${symbol}`).removeClass('price-updated');
                            }, 1000);
                        }
                    }
                    $('#price-update-time').text(data.time);
                    $('#price-update-time-mobile').text(data.time);
                } else {
                    // 更新失败
                    $('[id^="price-value-"]').html('<span class="text-danger small">更新失败</span>');
                }
            }

            // 获取实时价格
            function fetchRealTimePrices() {
                $.ajax({
                    url: '/api/prices',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        updatePriceDisplay(data);
                    },
                    error: function() {
                        $('[id^="price-value-"]').html('<span class="text-danger small">连接失败</span>');
                        showToast('价格更新失败，请检查网络连接', 'error');
                    }
                });
            }

            // 初始获取价格和活跃订单
            fetchRealTimePrices();
            fetchActiveOrders();

            // 设置自动更新
            priceUpdateInterval = setInterval(fetchRealTimePrices, 5000);  // 价格5秒更新
            activeOrdersUpdateInterval = setInterval(fetchActiveOrders, 10000);  // 活跃订单10秒更新

            // 手动刷新按钮
            $('#refreshPricesBtn').click(function() {
                const $btn = $(this);
                const $icon = $btn.find('i');

                // 添加加载状态
                $btn.prop('disabled', true);
                $icon.addClass('rotate-animation');

                fetchRealTimePrices();

                // 恢复按钮状态
                setTimeout(() => {
                    $btn.prop('disabled', false);
                    $icon.removeClass('rotate-animation');
                }, 1000);
            });

            // 获取活跃订单
            function fetchActiveOrders() {
                $.ajax({
                    url: '/api/active_orders',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        updateActiveOrdersDisplay(data);
                    },
                    error: function() {
                        showToast('获取活跃订单失败，请检查网络连接', 'error');
                    }
                });
            }

            // 更新活跃订单显示
            function updateActiveOrdersDisplay(data) {
                if (data.success) {
                    const orders = data.orders;
                    const $container = $('#active-orders-container');
                    const $count = $('#active-orders-count');

                    // 更新订单数量
                    $count.text(orders.length);

                    if (orders.length === 0) {
                        // 显示无订单状态
                        $container.html(`
                            <div class="text-center py-5" id="no-active-orders">
                                <div class="mb-3">
                                    <i class="bi bi-inbox fs-1 text-muted"></i>
                                </div>
                                <h5 class="text-muted">暂无活跃订单</h5>
                                <p class="text-muted mb-0">当前没有正在进行的交易</p>
                            </div>
                        `);
                    } else {
                        // 构建表格HTML
                        let tableHtml = `
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="active-orders-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col" class="border-0 fw-semibold">订单号</th>
                                            <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">交易对</th>
                                            <th scope="col" class="border-0 fw-semibold">方向</th>
                                            <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">交易量</th>
                                            <th scope="col" class="border-0 fw-semibold d-none d-md-table-cell">开仓价</th>
                                            <th scope="col" class="border-0 fw-semibold d-none d-lg-table-cell">止损/止盈</th>
                                            <th scope="col" class="border-0 fw-semibold">当前盈亏</th>
                                            <th scope="col" class="border-0 fw-semibold">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="active-orders-tbody">
                        `;

                        // 添加订单行
                        orders.forEach(order => {
                            const profitClass = order.profit > 0 ? 'text-success' : (order.profit < 0 ? 'text-danger' : 'text-muted');
                            const operationBadge = order.operation === 'buy' ?
                                '<span class="badge bg-success"><i class="bi bi-arrow-up"></i> 买入</span>' :
                                '<span class="badge bg-danger"><i class="bi bi-arrow-down"></i> 卖出</span>';

                            tableHtml += `
                                <tr class="align-middle">
                                    <td class="fw-bold">${order.ticket}</td>
                                    <td class="d-none d-md-table-cell">
                                        <span class="fw-semibold">${order.trading_pair}</span>
                                    </td>
                                    <td>
                                        ${operationBadge}
                                        <div class="d-md-none small text-muted mt-1">${order.trading_pair}</div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">${order.volume}</td>
                                    <td class="d-none d-md-table-cell">${order.price}</td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="small">
                                            <div>SL: ${order.sl || '-'}</div>
                                            <div>TP: ${order.tp || '-'}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold ${profitClass}">
                                            ${parseFloat(order.profit).toFixed(2)}
                                        </span>
                                        <div class="d-md-none small text-muted">
                                            Vol: ${order.volume} | Price: ${order.price}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/position/${order.ticket}" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye"></i>
                                                <span class="d-none d-lg-inline ms-1">详情</span>
                                            </a>
                                            <button class="btn btn-outline-danger btn-sm close-position-btn"
                                                    data-ticket="${order.ticket}"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#closePositionModal">
                                                <i class="bi bi-x-circle"></i>
                                                <span class="d-none d-lg-inline ms-1">平仓</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        });

                        tableHtml += `
                                    </tbody>
                                </table>
                            </div>
                        `;

                        $container.html(tableHtml);

                        // 重新绑定平仓按钮事件
                        $('.close-position-btn').off('click').on('click', function() {
                            const ticket = $(this).data('ticket');
                            $('#closePositionTicket').text(ticket);
                        });
                    }

                    // 添加更新动画效果
                    $container.addClass('price-updated');
                    setTimeout(() => {
                        $container.removeClass('price-updated');
                    }, 1000);
                } else {
                    showToast('获取活跃订单失败: ' + data.message, 'error');
                }
            }

            // 活跃订单刷新按钮
            $('#refreshActiveOrdersBtn').click(function() {
                const $btn = $(this);
                const $icon = $btn.find('i');

                // 添加加载状态
                $btn.prop('disabled', true);
                $icon.addClass('rotate-animation');

                fetchActiveOrders();

                // 恢复按钮状态
                setTimeout(() => {
                    $btn.prop('disabled', false);
                    $icon.removeClass('rotate-animation');
                }, 1000);
            });

            // 当页面隐藏时暂停更新
            $(document).on('visibilitychange', function() {
                if (document.hidden) {
                    clearInterval(priceUpdateInterval);
                    clearInterval(activeOrdersUpdateInterval);
                } else {
                    fetchRealTimePrices();  // 立即更新一次
                    fetchActiveOrders();    // 立即更新活跃订单
                    priceUpdateInterval = setInterval(fetchRealTimePrices, 5000);
                    activeOrdersUpdateInterval = setInterval(fetchActiveOrders, 10000);
                }
            });

            // 关闭所有持仓按钮事件
            $('#closeAllPositionsBtn').click(function() {
                // 使用现代化的确认对话框
                if (confirm('⚠️ 确定要平仓所有持仓吗？\n\n此操作将关闭所有交易对的所有持仓，且不可撤销。')) {
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // 添加加载状态
                    $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 处理中...');

                    $.ajax({
                        url: '/close_all_positions',
                        type: 'POST',
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showToast('平仓操作已成功执行: ' + response.message, 'success');
                                // 重新加载页面以显示最新状态
                                setTimeout(() => location.reload(), 1500);
                            } else {
                                showToast('平仓操作失败: ' + response.message, 'error');
                                $btn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function() {
                            showToast('平仓请求失败，请稍后重试', 'error');
                            $btn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });

            // 平仓盈利订单按钮事件
            $('#closeProfitablePositionsBtn').click(function() {
                // 使用现代化的确认对话框
                if (confirm('💰 确定要平仓所有盈利订单吗？\n\n此操作将关闭所有当前盈利的持仓，锁定盈利。')) {
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // 添加加载状态
                    $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 处理中...');

                    $.ajax({
                        url: '/close_profitable_positions',
                        type: 'POST',
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                let message = response.message;
                                if (response.total_profit) {
                                    message += ` (总盈利: $${response.total_profit.toFixed(2)})`;
                                }
                                showToast('盈利订单平仓操作已成功执行: ' + message, 'success');
                                // 重新加载页面以显示最新状态
                                setTimeout(() => location.reload(), 1500);
                            } else {
                                showToast('盈利订单平仓操作失败: ' + response.message, 'error');
                                $btn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function() {
                            showToast('盈利订单平仓请求失败，请稍后重试', 'error');
                            $btn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });
            
            // 确认平仓事件
            $('#confirmCloseBtn').click(function() {
                const ticket = $('#closePositionTicket').text();
                const $btn = $(this);
                const originalText = $btn.html();

                let data = {
                    ticket: ticket
                };

                if($('#partialCloseCheck').is(':checked')) {
                    const volume = $('#partialVolume').val();
                    if(volume && volume > 0) {
                        data.volume = volume;
                    } else {
                        showToast('请输入有效的平仓量', 'error');
                        return;
                    }
                }

                // 添加加载状态
                $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 处理中...');

                $.ajax({
                    url: '/close_position',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if(response.success) {
                            showToast('平仓成功', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('平仓失败: ' + response.message, 'error');
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function() {
                        showToast('请求失败，请稍后重试', 'error');
                        $btn.prop('disabled', false).html(originalText);
                    },
                    complete: function() {
                        if (!$btn.prop('disabled')) {
                            $('#closePositionModal').modal('hide');
                        }
                    }
                });
            });

            // 服务状态监控相关代码
            let serviceStatusInterval;
            
            // 更新服务状态显示
            function updateServiceStatus(data) {
                if (data.success) {
                    // 更新交易状态
                    const tradingEnabledBadge = $('.badge:contains("交易")');
                    tradingEnabledBadge.removeClass('bg-success bg-danger');
                    if (data.trading_enabled) {
                        tradingEnabledBadge.addClass('bg-success');
                        tradingEnabledBadge.html('<i class="bi bi-play-fill"></i> 交易已启用');
                    } else {
                        tradingEnabledBadge.addClass('bg-danger');
                        tradingEnabledBadge.html('<i class="bi bi-pause-fill"></i> 交易已暂停');
                    }
                    
                    // 更新MT5连接状态
                    const mt5Badge = $('.badge:contains("MT5")');
                    mt5Badge.removeClass('bg-success bg-danger');
                    if (data.mt5_connected) {
                        mt5Badge.addClass('bg-success');
                        mt5Badge.html('<i class="bi bi-check-circle-fill"></i> MT5已连接');
                    } else {
                        mt5Badge.addClass('bg-danger');
                        mt5Badge.html('<i class="bi bi-x-circle-fill"></i> MT5未连接');
                    }
                    
                    // 更新信号接收服务状态
                    const signalReceiverBadge = $('.badge:contains("信号接收")');
                    signalReceiverBadge.removeClass('bg-success bg-danger');
                    if (data.signal_receiver_running) {
                        signalReceiverBadge.addClass('bg-success');
                        signalReceiverBadge.html('<i class="bi bi-broadcast"></i> 信号接收服务运行中');
                    } else {
                        signalReceiverBadge.addClass('bg-danger');
                        signalReceiverBadge.html('<i class="bi bi-broadcast-off"></i> 信号接收服务未运行');
                    }
                }
            }
            
            // 获取服务状态
            function fetchServicesStatus() {
                $.ajax({
                    url: '/api/services_status',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        updateServiceStatus(data);
                    },
                    error: function() {
                        console.error("获取服务状态失败");
                    }
                });
            }
            
            // 初始获取服务状态
            fetchServicesStatus();
            
            // 设置30秒自动更新
            serviceStatusInterval = setInterval(fetchServicesStatus, 30000);
            
            // 当页面隐藏时暂停更新
            $(document).on('visibilitychange', function() {
                if (document.hidden) {
                    clearInterval(serviceStatusInterval);
                    clearInterval(accountInfoInterval);
                } else {
                    fetchServicesStatus();  // 立即更新一次
                    serviceStatusInterval = setInterval(fetchServicesStatus, 30000);
                    fetchAccountInfo(); // 立即更新一次
                    accountInfoInterval = setInterval(fetchAccountInfo, 10000);
                }
            });

            // 账户信息相关代码
            let accountInfoInterval;
            
            // 获取账户信息
            function fetchAccountInfo() {
                $.ajax({
                    url: '/api/account_info',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if(data.success) {
                            updateAccountInfo(data.account_info);
                        } else {
                            console.error("获取账户信息失败");
                        }
                    },
                    error: function() {
                        console.error("请求账户信息失败");
                    }
                });
            }
            
            // 更新账户信息显示
            function updateAccountInfo(account_info) {
                // 更新余额
                $('#account-balance').html((account_info.balance).toFixed(2) + " " + account_info.currency);

                // 更新净值
                $('#account-equity').html((account_info.equity).toFixed(2) + " " + account_info.currency);

                // 更新浮动盈亏 - 需要根据正负值调整颜色
                const profitFormatted = (account_info.profit).toFixed(2) + " " + account_info.currency;
                $('#account-profit').html(profitFormatted);

                // 更新浮动盈亏颜色（适配新的设计）
                if(account_info.profit >= 0) {
                    $('#account-profit').removeClass('text-danger text-muted').addClass('text-success');
                    $('#profit-icon').removeClass('bi-graph-down-arrow text-danger').addClass('bi-graph-up-arrow text-success');
                } else {
                    $('#account-profit').removeClass('text-success text-muted').addClass('text-danger');
                    $('#profit-icon').removeClass('bi-graph-up-arrow text-success').addClass('bi-graph-down-arrow text-danger');
                }

                // 更新可用保证金
                $('#account-free-margin').html((account_info.free_margin).toFixed(2) + " " + account_info.currency);

                // 添加更新动画效果
                $('#profit-card').addClass('price-updated');
                setTimeout(() => {
                    $('#profit-card').removeClass('price-updated');
                }, 1000);
            }
            
            // 手动刷新按钮点击事件
            $('#refreshAccountBtn').click(function() {
                $(this).find('i').addClass('rotate-animation');
                setTimeout(function() {
                    $('#refreshAccountBtn').find('i').removeClass('rotate-animation');
                }, 1000);
                fetchAccountInfo();
            });
            
            // 初始获取账户信息
            fetchAccountInfo();
            
            // 设置10秒自动更新
            accountInfoInterval = setInterval(fetchAccountInfo, 10000);

            // 添加键盘快捷键支持
            $(document).keydown(function(e) {
                // Ctrl/Cmd + R: 刷新价格
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 82) {
                    e.preventDefault();
                    $('#refreshPricesBtn').click();
                }

                // Ctrl/Cmd + A: 刷新账户信息
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 65) {
                    e.preventDefault();
                    $('#refreshAccountBtn').click();
                }
            });

            // 添加网络状态监控
            let isOnline = navigator.onLine;

            function updateNetworkStatus() {
                if (navigator.onLine && !isOnline) {
                    showToast('网络连接已恢复', 'success');
                    // 重新获取数据
                    fetchRealTimePrices();
                    fetchAccountInfo();
                    fetchServicesStatus();
                } else if (!navigator.onLine && isOnline) {
                    showToast('网络连接已断开', 'error');
                }
                isOnline = navigator.onLine;
            }

            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);

            // 添加触摸手势支持（移动端）
            if ('ontouchstart' in window) {
                let touchStartY = 0;
                let touchEndY = 0;

                $(document).on('touchstart', function(e) {
                    touchStartY = e.originalEvent.changedTouches[0].screenY;
                });

                $(document).on('touchend', function(e) {
                    touchEndY = e.originalEvent.changedTouches[0].screenY;
                    handleSwipe();
                });

                function handleSwipe() {
                    const swipeThreshold = 100;
                    const diff = touchStartY - touchEndY;

                    // 向上滑动刷新
                    if (diff > swipeThreshold && $(window).scrollTop() === 0) {
                        $('#refreshPricesBtn').click();
                    }
                }
            }

            // 页面性能监控
            if ('performance' in window) {
                window.addEventListener('load', function() {
                    setTimeout(function() {
                        const perfData = performance.getEntriesByType('navigation')[0];
                        if (perfData && perfData.loadEventEnd - perfData.loadEventStart > 3000) {
                            console.warn('页面加载较慢，建议检查网络连接');
                        }
                    }, 0);
                });
            }

            // 添加页面可见性变化时的处理
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    // 页面重新可见时，立即更新一次数据
                    setTimeout(() => {
                        fetchRealTimePrices();
                        fetchActiveOrders();
                        fetchAccountInfo();
                        fetchServicesStatus();
                    }, 500);
                }
            });
        });
    </script>
</body>
</html>
