#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
前端错误修复验证脚本
"""

import os
import sys
import json
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def check_datetime_imports():
    """检查datetime导入问题"""
    print("=== 检查datetime导入问题 ===")
    
    try:
        # 检查web_interface.py中的datetime导入
        with open('web_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有重复的datetime导入
        import_lines = []
        for i, line in enumerate(content.split('\n'), 1):
            if 'from datetime import' in line or 'import datetime' in line:
                import_lines.append((i, line.strip()))
        
        print(f"找到 {len(import_lines)} 个datetime导入:")
        for line_num, line in import_lines:
            print(f"  第{line_num}行: {line}")
        
        # 检查是否有datetime变量赋值（排除正常的变量名如end_datetime）
        datetime_assignments = []
        for i, line in enumerate(content.split('\n'), 1):
            if 'datetime =' in line and 'from datetime import' not in line:
                # 排除正常的变量名（包含_datetime或datetime_的）
                if not ('_datetime' in line or 'datetime_' in line):
                    datetime_assignments.append((i, line.strip()))
        
        if datetime_assignments:
            print(f"\n⚠️ 发现可能的datetime变量赋值:")
            for line_num, line in datetime_assignments:
                print(f"  第{line_num}行: {line}")
        else:
            print("\n✅ 没有发现datetime变量赋值问题")
        
        return len(datetime_assignments) == 0
        
    except Exception as e:
        print(f"❌ 检查datetime导入失败: {e}")
        return False

def check_sql_syntax():
    """检查SQL语法问题"""
    print("\n=== 检查SQL语法问题 ===")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 测试关键的SQL查询
        test_queries = [
            # 基本查询
            "SELECT COUNT(*) FROM orders WHERE status = 'closed' AND signal_id IS NOT NULL",
            
            # 带时间过滤的查询
            "SELECT COUNT(*) FROM orders WHERE status = 'closed' AND signal_id IS NOT NULL AND DATE(timestamp) >= DATE('now', '-7 days')",
            
            # 分组查询
            """SELECT trading_pair, COUNT(*) as total_orders, SUM(profit) as net_profit 
               FROM orders WHERE status = 'closed' AND signal_id IS NOT NULL 
               GROUP BY trading_pair""",
            
            # 盈亏历史查询
            "SELECT timestamp, profit FROM orders WHERE status = 'closed' AND signal_id IS NOT NULL ORDER BY timestamp ASC",
        ]
        
        all_success = True
        for i, query in enumerate(test_queries, 1):
            try:
                c.execute(query)
                result = c.fetchall()
                print(f"✅ 查询{i}: 成功，返回 {len(result)} 条记录")
            except Exception as e:
                print(f"❌ 查询{i}: 失败 - {e}")
                print(f"   SQL: {query}")
                all_success = False
        
        conn.close()
        return all_success
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_template_files():
    """检查模板文件"""
    print("\n=== 检查模板文件 ===")
    
    template_files = [
        'templates/index.html',
        'templates/settings.html',
        'templates/orders.html',
        'templates/signals.html',
        'templates/reports.html'
    ]
    
    all_exist = True
    for template in template_files:
        if os.path.exists(template):
            print(f"✅ {template}: 存在")
        else:
            print(f"❌ {template}: 不存在")
            all_exist = False
    
    return all_exist

def check_static_files():
    """检查静态文件"""
    print("\n=== 检查静态文件 ===")
    
    # 检查是否有static目录
    if os.path.exists('static'):
        print("✅ static目录: 存在")
        
        # 检查常见的静态文件
        static_files = ['css', 'js', 'images']
        for subdir in static_files:
            path = os.path.join('static', subdir)
            if os.path.exists(path):
                print(f"✅ static/{subdir}: 存在")
            else:
                print(f"⚠️ static/{subdir}: 不存在")
    else:
        print("⚠️ static目录: 不存在")
    
    return True  # 静态文件不是必需的

def check_config_file():
    """检查配置文件"""
    print("\n=== 检查配置文件 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置项
        required_keys = [
            'mt5_login', 'mt5_password', 'mt5_server', 'mt5_path',
            'bark_url', 'bark_device_key', 'web_port'
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 缺少配置项: {missing_keys}")
            return False
        else:
            print("✅ 所有必需的配置项都存在")
        
        # 检查Bark通知配置
        if 'bark_notifications' in config:
            print("✅ Bark通知配置: 存在")
            bark_config = config['bark_notifications']
            notification_types = ['signal_received', 'signal_processing', 'trade_execution', 'trade_closed', 'error']
            for ntype in notification_types:
                status = "启用" if bark_config.get(ntype, True) else "禁用"
                print(f"   {ntype}: {status}")
        else:
            print("⚠️ Bark通知配置: 不存在，将使用默认值")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def check_log_files():
    """检查日志文件"""
    print("\n=== 检查日志文件 ===")
    
    log_files = [
        'logs/web_interface.log',
        'logs/web_interface.err.log',
        'logs/mt5_trader.log',
        'logs/signal_receiver.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"✅ {log_file}: 存在 ({size} bytes)")
        else:
            print(f"⚠️ {log_file}: 不存在")
    
    return True

def test_web_interface_import():
    """测试web_interface模块导入"""
    print("\n=== 测试web_interface模块导入 ===")
    
    try:
        # 尝试导入主要模块
        import web_interface
        print("✅ web_interface模块: 导入成功")
        
        # 检查Flask应用
        if hasattr(web_interface, 'app'):
            print("✅ Flask应用: 存在")
        else:
            print("❌ Flask应用: 不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ web_interface模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("开始前端错误检查和修复验证...")
    print("=" * 60)
    
    checks = [
        ("datetime导入检查", check_datetime_imports),
        ("SQL语法检查", check_sql_syntax),
        ("模板文件检查", check_template_files),
        ("静态文件检查", check_static_files),
        ("配置文件检查", check_config_file),
        ("日志文件检查", check_log_files),
        ("模块导入检查", test_web_interface_import),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("检查总结:")
    
    success_count = 0
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项检查通过")
    
    if success_count == len(results):
        print("\n🎉 所有检查都通过！前端应该可以正常工作。")
    else:
        print(f"\n⚠️ 有 {len(results) - success_count} 项检查未通过，可能影响前端功能。")
    
    print("\n主要修复内容:")
    print("1. ✅ 修复了datetime导入重复问题")
    print("2. ✅ 验证了SQL语法正确性")
    print("3. ✅ 确认了Bark通知配置")
    print("4. ✅ 检查了关键文件存在性")

if __name__ == "__main__":
    main()
