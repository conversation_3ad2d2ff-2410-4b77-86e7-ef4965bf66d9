#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试SQL错误
"""

import sqlite3

def debug_sql_error():
    """调试SQL错误"""
    print("=== 调试SQL错误 ===")
    
    try:
        # 连接到数据库
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 模拟web_interface.py中的代码
        print("\n1. 模拟reports函数的SQL...")
        
        # 测试不同的period值
        test_periods = ['all', 'today', 'week', 'month']
        
        for period in test_periods:
            print(f"\n   测试period='{period}':")
            
            # 构建where_clause（模拟web_interface.py中的逻辑）
            where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"
            
            if period == 'today':
                where_clause += " AND DATE(timestamp) = DATE('now')"
            elif period == 'week':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
            elif period == 'month':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
            elif period == 'custom':
                # 模拟custom情况
                start_date = '2024-01-01'
                end_date = '2024-12-31'
                where_clause += f" AND DATE(timestamp) >= '{start_date}' AND DATE(timestamp) <= '{end_date}'"
            
            print(f"     where_clause: {where_clause}")
            
            # 测试总体统计SQL
            sql1 = f'''
                SELECT COUNT(*) as total_orders,
                       SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                       SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                       SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END) as total_profit,
                       SUM(CASE WHEN profit < 0 THEN profit ELSE 0 END) as total_loss,
                       SUM(profit) as net_profit
                FROM orders {where_clause}
            '''
            
            try:
                c.execute(sql1)
                result1 = c.fetchone()
                print(f"     总体统计: ✅ 成功")
            except Exception as e:
                print(f"     总体统计: ❌ 错误 - {e}")
                print(f"     SQL: {sql1}")
            
            # 测试按交易对统计SQL
            sql2 = f'''
                SELECT trading_pair,
                       COUNT(*) as total_orders,
                       SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                       SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                       SUM(profit) as net_profit
                FROM orders {where_clause}
                GROUP BY trading_pair
            '''
            
            try:
                c.execute(sql2)
                result2 = c.fetchall()
                print(f"     按交易对统计: ✅ 成功，返回 {len(result2)} 条记录")
            except Exception as e:
                print(f"     按交易对统计: ❌ 错误 - {e}")
                print(f"     SQL: {sql2}")
        
        # 测试api_profit_history函数的SQL
        print("\n2. 模拟api_profit_history函数的SQL...")
        
        for period in test_periods:
            print(f"\n   测试period='{period}':")
            
            where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"
            
            if period == 'today':
                where_clause += " AND DATE(timestamp) = DATE('now')"
            elif period == 'week':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
            elif period == 'month':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
            
            sql3 = f'''
                SELECT timestamp, profit
                FROM orders
                {where_clause}
                ORDER BY timestamp ASC
            '''
            
            try:
                c.execute(sql3)
                result3 = c.fetchall()
                print(f"     盈亏历史: ✅ 成功，返回 {len(result3)} 条记录")
            except Exception as e:
                print(f"     盈亏历史: ❌ 错误 - {e}")
                print(f"     SQL: {sql3}")
        
        # 测试可能的边界情况
        print("\n3. 测试边界情况...")
        
        # 测试空字符串参数
        test_cases = [
            ("custom", "", ""),  # 空的start_date和end_date
            ("custom", "2024-01-01", ""),  # 空的end_date
            ("custom", "", "2024-12-31"),  # 空的start_date
            ("invalid", "", ""),  # 无效的period
        ]
        
        for period, start_date, end_date in test_cases:
            print(f"\n   测试边界情况: period='{period}', start_date='{start_date}', end_date='{end_date}'")
            
            where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"
            
            if period == 'today':
                where_clause += " AND DATE(timestamp) = DATE('now')"
            elif period == 'week':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
            elif period == 'month':
                where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
            elif period == 'custom' and start_date and end_date:
                where_clause += f" AND DATE(timestamp) >= '{start_date}' AND DATE(timestamp) <= '{end_date}'"
            
            print(f"     where_clause: {where_clause}")
            
            sql = f'''
                SELECT timestamp, profit
                FROM orders
                {where_clause}
                ORDER BY timestamp ASC
            '''
            
            try:
                c.execute(sql)
                result = c.fetchall()
                print(f"     结果: ✅ 成功，返回 {len(result)} 条记录")
            except Exception as e:
                print(f"     结果: ❌ 错误 - {e}")
        
        conn.close()
        print("\n✅ SQL调试完成")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    debug_sql_error()
