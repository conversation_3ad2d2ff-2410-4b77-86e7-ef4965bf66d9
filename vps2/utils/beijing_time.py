import ntplib
from datetime import datetime, timedelta, timezone

BEIJING_TZ = timezone(timedelta(hours=8))


def get_beijing_time():
    """
    从NTP服务器获取当前北京时间（UTC+8）。
    如果NTP不可用，则回退为本地时间+8时区。
    """
    try:
        client = ntplib.NTPClient()
        # 使用中国的NTP服务器，或 pool.ntp.org
        response = client.request('ntp.aliyun.com', version=3, timeout=2)
        utc_time = datetime.utcfromtimestamp(response.tx_time)
        beijing_time = utc_time.replace(tzinfo=timezone.utc).astimezone(BEIJING_TZ)
        return beijing_time
    except Exception:
        # 回退为本地时间+8时区
        local_time = datetime.now(BEIJING_TZ)
        return local_time

if __name__ == "__main__":
    print("当前北京时间:", get_beijing_time().strftime('%Y-%m-%d %H:%M:%S'))
