#!/usr/bin/env python
# -*- coding: utf-8 -*-def try_order_with_filling_modes(request, direct_modes=None):
    """
    尝试使用不同的填充模式发送订单，直到成功或尝试所有模式
    
    参数:
        request: MT5订单请求字典
        direct_modes: 直接尝试特定的填充模式列表，若为None则使用默认顺序
        
    返回:
        字典格式的结果，包含success, message/error, 和result对象
    """
    symbol = request.get("symbol", "")der 5 交易填充模式辅助函数
"""

import MetaTrader5 as mt5
import logging

logger = logging.getLogger(__name__)

def get_supported_filling_modes(symbol):
    """
    获取交易品种支持的填充模式
    
    参数:
        symbol: MT5中的交易品种符号
    
    返回:
        支持的填充模式列表
    """
    try:
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            logger.error(f"无法获取交易品种 {symbol} 的信息")
            return []
        
        filling_modes = []
        flags = symbol_info.filling_mode
        
        # 检查支持的模式
        if flags & mt5.SYMBOL_FILLING_FOK:
            filling_modes.append(mt5.ORDER_FILLING_FOK)
        if flags & mt5.SYMBOL_FILLING_IOC:
            filling_modes.append(mt5.ORDER_FILLING_IOC)
        # RETURN 模式通常作为后备，几乎所有品种都支持
        filling_modes.append(mt5.ORDER_FILLING_RETURN)
        
        logger.info(f"交易品种 {symbol} 支持的填充模式: {filling_modes}")
        return filling_modes
    except Exception as e:
        logger.error(f"检查填充模式时出错: {e}")
        # 默认返回RETURN模式作为后备
        return [mt5.ORDER_FILLING_RETURN]

def try_order_with_filling_modes(request, direct_modes=None):
    """
    尝试使用不同的填充模式发送订单，直到成功或尝试所有模式
    
    参数:
        request: MT5订单请求字典
        direct_modes: 直接尝试特定的填充模式列表，若为None则使用默认顺序
        
    返回:
        MT5���单发送结果
    """
    symbol = request.get("symbol", "")
    
    # 如果没有指定特定模式，按优先级尝试不同的填充模式
    if not direct_modes:
        # 先获取交易品种支持的填充模式
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            # 优先使用交易品种支持的填充模式
            filling_modes = get_supported_filling_modes(symbol)
            if filling_modes:
                logger.info(f"使用交易品种 {symbol} 支持的填充模式: {filling_modes}")
                modes_to_try = filling_modes
            else:
                # 如果无法获取，使用默认顺序
                modes_to_try = [mt5.ORDER_FILLING_FOK, mt5.ORDER_FILLING_IOC, mt5.ORDER_FILLING_RETURN]
        else:
            # 交易品种信息不可用，使用默认顺序
            modes_to_try = [mt5.ORDER_FILLING_FOK, mt5.ORDER_FILLING_IOC, mt5.ORDER_FILLING_RETURN]
    else:
        modes_to_try = direct_modes
    
    result = None
    
    # 尝试每种填充模式
    for mode in modes_to_try:
        # 复制原始请求并修改填充模式
        current_request = request.copy()
        current_request["type_filling"] = mode
        
        mode_name = {
            mt5.ORDER_FILLING_FOK: "FOK (Fill or Kill)",
            mt5.ORDER_FILLING_IOC: "IOC (Immediate or Cancel)",
            mt5.ORDER_FILLING_RETURN: "RETURN"
        }.get(mode, str(mode))
        
        logger.info(f"尝试使用 {mode_name} 填充模式发送订单")
        result = mt5.order_send(current_request)
        
        # 如果成功或者错误不是因为填充模式，则退出循环
        if result:
            if result.retcode == 10009:  # 10009 = Order executed
                logger.info(f"订单成功执行!")
                break
            elif result.retcode != 10030:  # 10030 = Unsupported filling mode
                logger.warning(f"订单错误 (非填充模式错误): {result.retcode} - {result.comment}")
                break
    
    # 转换MT5的结果对象为一个更易于使用的字典格式
    if not result:
        # 如果所有模式都失败，创建一个错误结果
        logger.error("订单发送失败: 所有填充模式都失败")
        return {
            'success': False,
            'error': "所有填充模式都失败",
            'result': None
        }
    
    # 检查是否成功
    if result.retcode == mt5.TRADE_RETCODE_DONE or result.retcode == 10009:  # 10009 = 订单已执行
        logger.info(f"订单发送成功: {result.retcode} - {result.comment}")
        return {
            'success': True,
            'message': result.comment,
            'result': result,
            'order': result.order
        }
    else:
        error_msg = f"订单发送失败: {result.retcode} - {result.comment}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'result': result
        }
