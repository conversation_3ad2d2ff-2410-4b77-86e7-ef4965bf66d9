#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试MT5连接修复
"""

import MetaTrader5 as mt5
import json

def test_mt5_connection():
    """测试MT5连接功能"""
    print("=== 测试MT5连接修复 ===")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    mt5_login = config.get('mt5_login')
    mt5_password = config.get('mt5_password')
    mt5_server = config.get('mt5_server')
    mt5_path = config.get('mt5_path')
    
    print(f"配置信息:")
    print(f"  登录ID: {mt5_login}")
    print(f"  服务器: {mt5_server}")
    print(f"  路径: {mt5_path}")
    
    # 关闭现有连接
    mt5.shutdown()
    
    # 测试1: 默认初始化
    print("\n1. 测试默认初始化...")
    try:
        if mt5.initialize():
            print("✅ 默认初始化成功")
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"   实际路径: {terminal_info.path}")
            
            # 尝试登录
            if mt5.login(login=int(mt5_login), password=mt5_password, server=mt5_server):
                print("✅ 登录成功")
                account_info = mt5.account_info()
                if account_info:
                    print(f"   账户: {account_info.login}")
                    print(f"   服务器: {account_info.server}")
                    print(f"   余额: {account_info.balance}")
                mt5.shutdown()
                return True
            else:
                error = mt5.last_error()
                print(f"❌ 登录失败: {error}")
        else:
            error = mt5.last_error()
            print(f"❌ 默认初始化失败: {error}")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试2: 指定路径初始化
    if mt5_path:
        print(f"\n2. 测试指定路径初始化: {mt5_path}")
        try:
            if mt5.initialize(path=mt5_path):
                print("✅ 指定路径初始化成功")
                
                # 尝试登录
                if mt5.login(login=int(mt5_login), password=mt5_password, server=mt5_server):
                    print("✅ 登录成功")
                    account_info = mt5.account_info()
                    if account_info:
                        print(f"   账户: {account_info.login}")
                        print(f"   服务器: {account_info.server}")
                        print(f"   余额: {account_info.balance}")
                    mt5.shutdown()
                    return True
                else:
                    error = mt5.last_error()
                    print(f"❌ 登录失败: {error}")
            else:
                error = mt5.last_error()
                print(f"❌ 指定路径初始化失败: {error}")
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    return False

def test_current_connection():
    """测试当前连接状态"""
    print("\n=== 当前连接状态 ===")
    
    try:
        import mt5_trader
        
        # 检查mt5_trader模块的连接状态
        print(f"mt5_trader初始化状态: {mt5_trader.mt5_initialized}")
        
        # 尝试重新初始化
        result = mt5_trader.init_mt5()
        print(f"重新初始化结果: {result}")
        
        if result:
            # 获取账户信息
            account_info = mt5_trader.get_account_info()
            print(f"账户信息: {account_info}")
            
            # 检查终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"终端路径: {terminal_info.path}")
                print(f"终端名称: {terminal_info.name}")
        
        return result
        
    except Exception as e:
        print(f"❌ 检查当前连接失败: {e}")
        return False

def main():
    print("开始MT5连接测试...")
    
    # 测试当前连接
    current_ok = test_current_connection()
    
    # 测试连接功能
    connection_ok = test_mt5_connection()
    
    print("\n=== 测试总结 ===")
    print(f"当前连接状态: {'✅ 正常' if current_ok else '❌ 异常'}")
    print(f"连接测试功能: {'✅ 正常' if connection_ok else '❌ 异常'}")
    
    if current_ok:
        print("\n💡 建议:")
        print("1. 系统的MT5连接实际上是正常的")
        print("2. Web界面测试连接的问题可能是路径配置问题")
        print("3. 可以尝试在Web界面中清空MT5路径字段，然后测试连接")
        print("4. 或者更新配置文件中的正确MT5路径")

if __name__ == "__main__":
    main()
