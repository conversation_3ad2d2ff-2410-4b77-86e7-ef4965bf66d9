# PowerShell script to run the testing environment
Write-Host "正在启动交易测试环境..." -ForegroundColor Cyan

# Current directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Start the monitoring tool
Start-Process PowerShell -ArgumentList "-NoExit", "-Command", "cd '$scriptPath'; python monitor_trades.py"

# Wait for monitor to start
Start-Sleep -Seconds 3

# Start the testing tool
Start-Process PowerShell -ArgumentList "-NoExit", "-Command", "cd '$scriptPath'; python test_sl_tp_config.py"

Write-Host "测试环境已启动，请观察窗口输出结果" -ForegroundColor Green
