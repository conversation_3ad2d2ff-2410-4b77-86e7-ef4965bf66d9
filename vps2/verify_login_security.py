#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证登录安全功能是否正常工作
"""

import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("=== 测试导入 ===")
    
    try:
        import PIL
        print(f"✅ PIL/Pillow: {PIL.__version__}")
    except ImportError as e:
        print(f"❌ PIL/Pillow导入失败: {e}")
        return False
    
    try:
        import flask
        print(f"✅ Flask: {flask.__version__}")
    except ImportError as e:
        print(f"❌ Flask导入失败: {e}")
        return False
    
    try:
        import sqlite3
        print(f"✅ SQLite3: {sqlite3.sqlite_version}")
    except ImportError as e:
        print(f"❌ SQLite3导入失败: {e}")
        return False
    
    return True

def test_web_interface_functions():
    """测试web_interface模块的关键函数"""
    print("\n=== 测试Web界面函数 ===")
    
    try:
        from web_interface import (
            init_login_security_tables,
            generate_captcha_code,
            create_captcha_image,
            save_captcha_to_session,
            verify_captcha,
            record_login_attempt,
            get_failed_login_count,
            should_require_captcha
        )
        print("✅ 所有登录安全函数导入成功")
        
        # 测试验证码生成
        code = generate_captcha_code()
        print(f"✅ 验证码生成: {code}")
        
        # 测试验证码图片生成
        image = create_captcha_image(code)
        print(f"✅ 验证码图片生成: {image.size}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        return False

def test_database_tables():
    """测试数据库表是否正确创建"""
    print("\n=== 测试数据库表 ===")
    
    try:
        import sqlite3
        
        # 初始化表
        from web_interface import init_login_security_tables
        init_login_security_tables()
        
        # 检查表是否存在
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('login_attempts', 'captcha_sessions')")
        tables = [row[0] for row in c.fetchall()]
        
        if 'login_attempts' in tables:
            print("✅ login_attempts表存在")
        else:
            print("❌ login_attempts表不存在")
            return False
            
        if 'captcha_sessions' in tables:
            print("✅ captcha_sessions表存在")
        else:
            print("❌ captcha_sessions表不存在")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库表测试失败: {e}")
        return False

def test_login_flow():
    """测试完整的登录流程"""
    print("\n=== 测试登录流程 ===")
    
    try:
        from web_interface import (
            record_login_attempt,
            get_failed_login_count,
            should_require_captcha,
            save_captcha_to_session,
            verify_captcha
        )
        
        test_ip = '127.0.0.1'
        
        # 清理测试数据
        import sqlite3
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        c.execute("DELETE FROM login_attempts WHERE ip_address = ?", (test_ip,))
        c.execute("DELETE FROM captcha_sessions WHERE session_id = 'test_session'")
        conn.commit()
        conn.close()
        
        # 测试正常情况（不需要验证码）
        failed_count = get_failed_login_count(test_ip)
        require_captcha = should_require_captcha(test_ip)
        print(f"✅ 初始状态: 失败次数={failed_count}, 需要验证码={require_captcha}")
        
        # 模拟3次失败登录
        for i in range(3):
            record_login_attempt(test_ip, 'test_user', False)
            failed_count = get_failed_login_count(test_ip)
            require_captcha = should_require_captcha(test_ip)
            print(f"✅ 第{i+1}次失败后: 失败次数={failed_count}, 需要验证码={require_captcha}")
        
        # 测试验证码功能
        if should_require_captcha(test_ip):
            print("✅ 验证码已启用")
            
            # 保存验证码
            test_code = 'TEST'
            if save_captcha_to_session('test_session', test_code):
                print("✅ 验证码保存成功")
                
                # 验证正确验证码
                if verify_captcha('test_session', test_code):
                    print("✅ 验证码验证成功")
                else:
                    print("❌ 验证码验证失败")
                    return False
            else:
                print("❌ 验证码保存失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 登录流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始验证登录安全功能...\n")
    
    success = True
    success &= test_imports()
    success &= test_web_interface_functions()
    success &= test_database_tables()
    success &= test_login_flow()
    
    print("\n" + "="*50)
    if success:
        print("🎉 所有验证通过！登录安全功能已正确实现。")
        print("\n功能摘要:")
        print("- ✅ 验证码生成和验证")
        print("- ✅ 登录尝试跟踪")
        print("- ✅ 1分钟内3次失败后启用验证码")
        print("- ✅ 数据库表正确创建")
        print("- ✅ 所有依赖包正常工作")
        print("\n现在可以启动Web界面测试登录功能！")
    else:
        print("⚠️ 部分验证失败，请检查相关功能。")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
