#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Bark通知设置功能
"""

import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_notification_settings():
    """测试通知设置功能"""
    print("=" * 60)
    print("测试Bark通知设置功能")
    print("=" * 60)
    
    try:
        # 导入模块
        import bark_notifier
        
        # 测试1: 检查默认配置
        print("\n1. 测试默认通知配置...")
        config = bark_notifier.load_config()
        
        # 如果没有bark_notifications配置，应该使用默认值
        if 'bark_notifications' not in config:
            print("   配置中没有bark_notifications，将使用默认值")
            default_notifications = {
                'signal_received': True,
                'signal_processing': True,
                'trade_execution': True,
                'trade_closed': True,
                'error': True
            }
            config['bark_notifications'] = default_notifications
        
        print(f"   当前通知设置: {config['bark_notifications']}")
        
        # 测试2: 检查通知开关功能
        print("\n2. 测试通知开关功能...")
        for notification_type in ['signal_received', 'signal_processing', 'trade_execution', 'trade_closed', 'error']:
            enabled = bark_notifier.is_notification_enabled(notification_type, config)
            print(f"   {notification_type}: {'✅ 启用' if enabled else '❌ 禁用'}")
        
        # 测试3: 测试禁用某个通知类型
        print("\n3. 测试禁用信号接收通知...")
        test_config = config.copy()
        test_config['bark_notifications'] = config['bark_notifications'].copy()
        test_config['bark_notifications']['signal_received'] = False
        
        enabled = bark_notifier.is_notification_enabled('signal_received', test_config)
        print(f"   信号接收通知: {'✅ 启用' if enabled else '❌ 禁用'}")
        
        # 测试4: 测试发送通知时的开关检查
        print("\n4. 测试发送通知时的开关检查...")
        
        # 测试启用的通知
        result1 = bark_notifier.send_bark_notification(
            "测试通知", 
            "这是一个启用的通知测试", 
            config, 
            'trade_execution'
        )
        print(f"   交易执行通知（启用）: {'✅ 发送成功' if result1 else '❌ 发送失败'}")
        
        # 测试禁用的通知
        result2 = bark_notifier.send_bark_notification(
            "测试通知", 
            "这是一个禁用的通知测试", 
            test_config, 
            'signal_received'
        )
        print(f"   信号接收通知（禁用）: {'✅ 跳过发送' if result2 else '❌ 处理失败'}")
        
        # 测试5: 测试各种通知函数
        print("\n5. 测试各种通知函数...")
        
        # 交易执行通知（重要，应该始终启用）
        test_trade_info = {
            'ticket': 123456,
            'symbol': 'ETHUSD',
            'operation': 'buy',
            'volume': 0.1,
            'price': 3500.0,
            'sl': 3400.0,
            'tp': 3600.0
        }
        result_trade = bark_notifier.notify_trade_execution(test_trade_info)
        print(f"   交易执行通知: {'✅ 成功' if result_trade else '❌ 失败'}")
        
        # 信号接收通知（使用禁用配置）
        test_signal_info = {
            'signal_id': 12345,
            'trading_pair': 'ETHUSD',
            'signal_type': 'buy',
            'interval': '1h',
            'mrc_event': 'entry'
        }
        
        # 使用原始配置（启用）
        result_signal1 = bark_notifier.notify_signal_received(test_signal_info)
        print(f"   信号接收通知（启用）: {'✅ 成功' if result_signal1 else '❌ 失败'}")
        
        # 临时修改配置来测试禁用
        original_config = bark_notifier.load_config()
        bark_notifier.config = test_config  # 临时替换
        result_signal2 = bark_notifier.notify_signal_received(test_signal_info)
        bark_notifier.config = original_config  # 恢复原配置
        print(f"   信号接收通知（禁用）: {'✅ 跳过' if result_signal2 else '❌ 失败'}")
        
        print("\n" + "=" * 60)
        print("测试总结:")
        print("✅ 通知设置功能正常工作")
        print("✅ 通知开关检查功能正常")
        print("✅ 各种通知类型都支持开关控制")
        print("✅ 交易执行通知功能正常（最重要）")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_config_update():
    """测试配置更新功能"""
    print("\n" + "=" * 60)
    print("测试配置更新功能")
    print("=" * 60)
    
    try:
        # 读取当前配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"当前配置文件中的bark_notifications: {config.get('bark_notifications', '未配置')}")
        
        # 如果没有配置，添加默认配置
        if 'bark_notifications' not in config:
            print("\n添加默认通知配置到config.json...")
            config['bark_notifications'] = {
                'signal_received': True,
                'signal_processing': True,
                'trade_execution': True,
                'trade_closed': True,
                'error': True
            }
            
            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            print("✅ 默认通知配置已添加到config.json")
        else:
            print("✅ 配置文件中已存在bark_notifications设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")
        return False

def main():
    """主函数"""
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试配置更新
    config_test_ok = test_config_update()
    
    # 测试通知设置功能
    settings_test_ok = test_notification_settings()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if config_test_ok and settings_test_ok:
        print("\n🎉 所有测试通过！")
        print("🔔 Bark通知设置功能已完全实现！")
        print("📱 现在可以在Web界面中选择启用或禁用不同类型的通知！")
        print("\n💡 重要提醒：")
        print("   - 交易执行通知建议始终保持开启")
        print("   - 可以根据需要选择性开启其他通知类型")
        print("   - 所有设置都会实时生效")
    else:
        print("\n⚠️ 部分测试未通过，请检查配置和日志。")

if __name__ == "__main__":
    main()
