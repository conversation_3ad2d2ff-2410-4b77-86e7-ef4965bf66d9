#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试信号处理的Bark通知功能
"""

import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_signal_notifications():
    """测试信号相关的所有Bark通知功能"""
    print("=" * 60)
    print("测试信号处理的Bark通知功能")
    print("=" * 60)
    
    try:
        # 导入模块
        import bark_notifier
        
        # 测试1: 信号接收通知
        print("\n1. 测试信号接收通知...")
        test_signal_info = {
            'signal_id': 12345,
            'trading_pair': 'ETHUSD',
            'signal_type': 'buy',
            'interval': '1h',
            'mrc_event': 'entry',
            'timestamp': datetime.now().isoformat()
        }
        result1 = bark_notifier.notify_signal_received(test_signal_info)
        print(f"   信号接收通知结果: {'成功' if result1 else '失败'}")
        
        # 测试2: 信号处理开始通知
        print("\n2. 测试信号处理开始通知...")
        test_signal_dict = {
            'id': 12345,
            'trading_pair': 'ETHUSD',
            'signal_type': 'buy',
            'interval': '1h'
        }
        result2 = bark_notifier.notify_signal_processing(test_signal_dict, 'started')
        print(f"   信号处理开始通知结果: {'成功' if result2 else '失败'}")
        
        # 测试3: 信号处理成功通知
        print("\n3. 测试信号处理成功通知...")
        result3 = bark_notifier.notify_signal_processing(
            test_signal_dict, 
            'success', 
            '信号处理成功，交易已执行'
        )
        print(f"   信号处理成功通知结果: {'成功' if result3 else '失败'}")
        
        # 测试4: 信号处理失败通知
        print("\n4. 测试信号处理失败通知...")
        result4 = bark_notifier.notify_signal_processing(
            test_signal_dict, 
            'failed', 
            '交易品种已禁用'
        )
        print(f"   信号处理失败通知结果: {'成功' if result4 else '失败'}")
        
        # 测试5: 信号跳过处理通知
        print("\n5. 测试信号跳过处理通知...")
        result5 = bark_notifier.notify_signal_processing(
            test_signal_dict, 
            'skipped', 
            '信号已经处理过'
        )
        print(f"   信号跳过处理通知结果: {'成功' if result5 else '失败'}")
        
        # 测试6: 交易执行通知（已存在的功能）
        print("\n6. 测试交易执行通知...")
        test_trade_info = {
            'ticket': 123456,
            'symbol': 'ETHUSD',
            'operation': 'buy',
            'volume': 0.1,
            'price': 3500.0,
            'sl': 3400.0,
            'tp': 3600.0,
            'signal_id': 12345
        }
        result6 = bark_notifier.notify_trade_execution(test_trade_info)
        print(f"   交易执行通知结果: {'成功' if result6 else '失败'}")
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结:")
        all_results = [result1, result2, result3, result4, result5, result6]
        all_success = all(all_results)
        success_count = sum(all_results)
        
        print(f"总测试数: {len(all_results)}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {len(all_results) - success_count}")
        print(f"总体结果: {'全部成功' if all_success else '部分失败'}")
        
        if all_success:
            print("\n✅ 所有信号通知功能正常工作！")
            print("✅ 现在系统会在以下情况发送通知：")
            print("   📡 接收到webhook信号时")
            print("   ⚡ 开始处理信号时")
            print("   ✅ 信号处理成功时")
            print("   ❌ 信号处理失败时")
            print("   ⏭️ 跳过已处理信号时")
            print("   💰 执行交易时")
            print("   🔄 平仓时")
        else:
            print("❌ 部分通知功能异常，请检查配置和日志")
            
        return all_success
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def simulate_signal_workflow():
    """模拟完整的信号处理工作流程"""
    print("\n" + "=" * 60)
    print("模拟完整信号处理工作流程")
    print("=" * 60)
    
    try:
        import bark_notifier
        
        # 模拟信号数据
        signal_data = {
            'trading_pair': 'BTCUSD',
            'signal_type': 'sell',
            'interval': '4h',
            'mrc_event': 'entry',
            'timestamp': datetime.now().isoformat(),
            'mean': 45000.0,
            'close_price': 45100.0
        }
        
        print("\n步骤1: 模拟接收webhook信号...")
        signal_data['signal_id'] = 99999
        result1 = bark_notifier.notify_signal_received(signal_data)
        print(f"   信号接收通知: {'✅' if result1 else '❌'}")
        
        print("\n步骤2: 模拟开始处理信号...")
        signal_dict = {
            'id': 99999,
            'trading_pair': 'BTCUSD',
            'signal_type': 'sell'
        }
        result2 = bark_notifier.notify_signal_processing(signal_dict, 'started')
        print(f"   开始处理通知: {'✅' if result2 else '❌'}")
        
        print("\n步骤3: 模拟交易执行...")
        trade_info = {
            'ticket': 999999,
            'symbol': 'BTCUSD',
            'operation': 'sell',
            'volume': 0.05,
            'price': 45100.0,
            'sl': 45200.0,
            'tp': 44900.0,
            'signal_id': 99999
        }
        result3 = bark_notifier.notify_trade_execution(trade_info)
        print(f"   交易执行通知: {'✅' if result3 else '❌'}")
        
        print("\n步骤4: 模拟处理成功...")
        result4 = bark_notifier.notify_signal_processing(
            signal_dict, 
            'success', 
            '信号处理成功，交易已执行'
        )
        print(f"   处理成功通知: {'✅' if result4 else '❌'}")
        
        all_success = all([result1, result2, result3, result4])
        
        print(f"\n🎯 完整工作流程测试: {'✅ 成功' if all_success else '❌ 失败'}")
        
        if all_success:
            print("\n🎉 完美！现在用户会收到完整的信号处理流程通知：")
            print("   1️⃣ 信号接收确认")
            print("   2️⃣ 处理开始提醒")
            print("   3️⃣ 交易执行详情")
            print("   4️⃣ 处理结果确认")
        
        return all_success
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {e}")
        return False

def main():
    """主函数"""
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础功能测试
    basic_test_ok = test_signal_notifications()
    
    # 工作流程测试
    workflow_test_ok = simulate_signal_workflow()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if basic_test_ok and workflow_test_ok:
        print("\n🎉 所有测试通过！")
        print("🔔 现在webhook信号处理的每个步骤都会发送Bark通知！")
        print("📱 两个Bark设备都会收到完整的处理流程通知！")
    else:
        print("\n⚠️ 部分测试未通过，请检查配置和日志。")

if __name__ == "__main__":
    main()
