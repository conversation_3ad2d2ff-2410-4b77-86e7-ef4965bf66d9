# MetaTrader5 Web界面配置功能说明

## 功能概述

我们已经成功为Web界面添加了MetaTrader5服务器和账号密码的修改功能，用户现在可以通过Web界面直接管理MT5连接配置。

## 新增功能

### 1. MT5连接配置表单
- **位置**: 设置页面顶部的"MetaTrader 5 连接配置"卡片
- **功能**: 允许用户修改MT5登录信息
- **字段**:
  - MT5账号ID（必须是纯数字）
  - MT5密码（密码字段，安全输入）
  - MT5服务器（支持自动建议常见服务器名称）
  - MT5安装路径（可选，默认使用标准路径）

### 2. 环境检查功能
- **按钮**: "检查环境"
- **功能**: 
  - 检测MT5是否已安装
  - 显示找到的MT5安装路径
  - 检查MT5进程是否运行
  - 验证Python MT5库是否可用

### 3. 连接测试功能
- **按钮**: "测试连接"
- **功能**:
  - 使用输入的配置测试MT5连接
  - 显示详细的错误信息和解决方案
  - 成功时显示账户信息验证

### 4. 自动连接重启
- **触发**: 保存MT5配置后自动执行
- **功能**: 
  - 关闭现有MT5连接
  - 使用新配置重新初始化连接
  - 后台执行，不阻塞用户界面

## 错误处理和用户指导

### 常见错误及解决方案

#### 1. 授权失败 (Authorization failed)
**错误代码**: -6
**可能原因**:
- 账号ID、密码或服务器名称错误
- 账户被锁定或暂停
- 服务器名称格式不正确

**解决方案**:
1. 检查账号ID、密码是否正确
2. 确认服务器名称是否准确（区分大小写）
3. 确保账户未被锁定或暂停
4. 尝试先在MT5客户端手动登录验证账户信息
5. 联系经纪商确认账户状态

#### 2. 初始化失败
**错误代码**: -1 或其他
**可能原因**:
- MT5未安装或路径错误
- MT5版本不兼容
- 权限问题

**解决方案**:
1. 确保MetaTrader 5客户端已安装并能正常启动
2. 检查MT5安装路径是否正确
3. 尝试先手动启动MT5客户端，然后再测试连接
4. 确保MT5客户端版本与Python库兼容

## 使用流程

### 推荐操作步骤：
1. **环境检查**: 点击"检查环境"按钮，确认MT5安装状态
2. **填写信息**: 输入完整的MT5账户信息
3. **测试连接**: 点击"测试连接"验证配置
4. **保存设置**: 测试成功后点击"保存设置"

### 服务器名称建议
系统提供了常见经纪商的服务器名称建议：
- Tickmill: `Tickmill-Demo`, `Tickmill-Live`, `demo.mt5tickmill.com`
- XM: `XM-Demo`, `XM-Real`
- FXTM: `FXTM-Demo`, `FXTM-Real`
- Exness: `Exness-Demo`, `Exness-Real`
- IC Markets: `IC Markets-Demo`, `IC Markets-Live`

## 安全特性

1. **密码字段**: MT5密码使用password类型输入框，确保输入安全
2. **配置验证**: 保存前进行格式验证（如账号ID必须是数字）
3. **连接测试**: 提供独立的测试功能，避免错误配置影响生产环境
4. **详细日志**: 记录所有连接尝试和错误信息，便于问题排查

## 技术实现

### 后端API端点
- `POST /api/test_mt5_connection`: 测试MT5连接
- `GET /api/check_mt5_environment`: 检查MT5环境
- `POST /settings`: 保存配置（已扩展支持MT5配置）

### 前端功能
- 实时表单验证
- 异步连接测试
- 详细错误信息显示
- 用户友好的操作指导

## 配置文件更新

修改的配置字段会自动更新到 `config.json` 文件中：
```json
{
    "mt5_login": "账号ID",
    "mt5_password": "密码",
    "mt5_server": "服务器名称",
    "mt5_path": "安装路径"
}
```

## 注意事项

1. **权限要求**: 确保Web应用有权限读写配置文件
2. **MT5版本**: 建议使用最新版本的MetaTrader 5客户端
3. **网络连接**: 确保服务器能够访问MT5经纪商服务器
4. **防火墙**: 检查防火墙设置，确保不会阻止MT5连接

这个功能大大简化了MT5配置管理，用户无需手动编辑配置文件，通过Web界面即可完成所有设置。
