#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5交易模块 - 使用mt5linux连接Wine环境中的MetaTrader5
"""

from mt5linux import MetaTrader5
import time
import logging
import os
import json
import sys
import sqlite3
from datetime import datetime, timezone
import threading
import bark_notifier
from utils.beijing_time import get_beijing_time

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    filename='logs/mt5_trader.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库操作的线程锁
DB_LOCK = threading.Lock()

# 全局配置
config = {}
symbol_map = {}
mt5_initialized = False
mt5 = None

# 交易品种小数位配置
SYMBOL_DIGITS = {
    "XAUUSD": 2,   # 黄金 - 2位小数
    "BTCUSD": 1,   # 比特币 - 1位小数
    "ETHUSD": 1,   # 以太坊 - 1位小数
    "GBPJPY": 3,   # 英镑/日元 - 3位小数
    "BRENT": 2,    # 布伦特原油 - 2位小数
    "XTIUSD": 2,   # WTI原油 - 2位小数
    "GBPUSD": 5,   # 英镑/美元 - 5位小数
    "DXY": 3,      # 美元指数 - 3位小数
}

def load_config():
    """加载配置文件"""
    global config, symbol_map
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        symbol_map = config.get('symbol_map', {})
        logger.info("配置文件加载成功")
        return True
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return False

def init_mt5():
    """初始化MT5连接 - 使用mt5linux连接到Wine环境"""
    global mt5_initialized, mt5
    
    try:
        # 连接到MT5Linux服务器 (运行在8001端口)
        mt5 = MetaTrader5(host='localhost', port=8001)
        
        # 初始化连接
        if not mt5.initialize():
            logger.error("MT5Linux初始化失败")
            return False
        
        # 获取MT5版本信息
        version = mt5.version()
        logger.info(f"MT5Linux连接成功，版本: {version}")
        
        # 登录MT5账户
        login = config.get('mt5_login')
        password = config.get('mt5_password')
        server = config.get('mt5_server')
        
        if login and password and server:
            if mt5.login(login, password, server):
                logger.info(f"MT5账户登录成功: {login}@{server}")
                mt5_initialized = True
                return True
            else:
                logger.error(f"MT5账户登录失败: {login}@{server}")
                return False
        else:
            logger.warning("MT5账户信息未配置，跳过登录")
            mt5_initialized = True
            return True
            
    except Exception as e:
        logger.error(f"MT5初始化失败: {e}")
        return False

def get_account_info():
    """获取账户信息"""
    if not mt5_initialized or not mt5:
        return None
    
    try:
        account_info = mt5.account_info()
        if account_info:
            return {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'profit': account_info.profit,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'currency': account_info.currency
            }
    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
    
    return None

def get_symbol_info(symbol):
    """获取交易品种信息"""
    if not mt5_initialized or not mt5:
        return None
    
    try:
        symbol_info = mt5.symbol_info(symbol)
        return symbol_info
    except Exception as e:
        logger.error(f"获取品种信息失败 {symbol}: {e}")
        return None

def execute_trade(signal):
    """执行交易信号"""
    global mt5
    
    # 确保使用最新配置
    load_config()
    
    if not mt5_initialized and not init_mt5():
        error_msg = "MT5未初始化，无法执行交易"
        logger.error(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg,
                           process_status='failed', failure_reason=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
    
    # 检查交易开关
    if not config.get('enable_trading', False):
        error_msg = "交易功能已关闭"
        logger.warning(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg,
                           process_status='failed', failure_reason="交易功能关闭")
        return False
    
    try:
        # 获取交易参数
        trading_pair = signal.get('trading_pair', '')
        signal_type = signal.get('signal_type', '').lower()
        signal_id = signal.get('id')
        
        # 映射交易品种
        mt5_symbol = symbol_map.get(trading_pair, trading_pair)
        
        # 获取品种信息
        symbol_info = get_symbol_info(mt5_symbol)
        if not symbol_info:
            error_msg = f"无法获取品种信息: {mt5_symbol}"
            logger.error(error_msg)
            update_signal_status(signal_id, processed=True, order_result=error_msg,
                               process_status='failed', failure_reason=error_msg)
            return False
        
        # 获取当前价格
        if signal_type == 'buy':
            price = symbol_info.ask
            order_type = mt5.ORDER_TYPE_BUY
        else:
            price = symbol_info.bid
            order_type = mt5.ORDER_TYPE_SELL
        
        # 获取交易量
        volume = config.get('symbol_volumes', {}).get(trading_pair, config.get('default_volume', 0.1))
        
        # 计算止损止盈
        sl_points = config.get('symbol_sl_points', {}).get(trading_pair, config.get('default_sl_points', 400))
        tp_points = config.get('symbol_tp_points', {}).get(trading_pair, config.get('default_tp_points', 400))
        
        point = symbol_info.point
        if signal_type == 'buy':
            sl = price - sl_points * point if sl_points > 0 else 0
            tp = price + tp_points * point if tp_points > 0 else 0
        else:
            sl = price + sl_points * point if sl_points > 0 else 0
            tp = price - tp_points * point if tp_points > 0 else 0
        
        # 构建交易请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": mt5_symbol,
            "volume": float(volume),
            "type": order_type,
            "price": price,
            "deviation": 10,
            "magic": 12345,
            "comment": f"Signal ID: {signal_id}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        
        if sl > 0:
            request['sl'] = sl
        if tp > 0:
            request['tp'] = tp
        
        logger.info(f"发送交易请求: {json.dumps(request, default=str)}")
        
        # 执行交易
        result = mt5.order_send(request)
        
        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            success_msg = f"交易执行成功: 订单号 {result.order}, 价格 {result.price}"
            logger.info(success_msg)
            
            # 更新信号状态
            update_signal_status(signal_id, processed=True, 
                               order_ticket=result.order,
                               order_result=success_msg,
                               process_status='success')
            
            # 发送成功通知
            bark_notifier.notify_trade_execution({
                'signal_id': signal_id,
                'symbol': mt5_symbol,
                'type': signal_type,
                'volume': volume,
                'price': result.price,
                'order_id': result.order,
                'status': 'success'
            })
            
            return True
        else:
            error_msg = f"交易执行失败: {result.comment if result else 'Unknown error'}"
            logger.error(error_msg)
            
            update_signal_status(signal_id, processed=True, order_result=error_msg,
                               process_status='failed', failure_reason=error_msg)
            
            bark_notifier.notify_error(f"交易执行失败: {error_msg}")
            return False
            
    except Exception as e:
        error_msg = f"执行交易时发生异常: {e}"
        logger.error(error_msg, exc_info=True)
        
        update_signal_status(signal.get('id'), processed=True, order_result=error_msg,
                           process_status='failed', failure_reason=str(e))
        
        bark_notifier.notify_error(error_msg)
        return False

def update_signal_status(signal_id, processed=False, order_ticket=None, order_result=None, 
                        process_status=None, failure_reason=None):
    """更新信号处理状态"""
    try:
        with DB_LOCK:
            conn = sqlite3.connect('trading_data.db')
            c = conn.cursor()
            
            update_fields = []
            params = []
            
            if processed is not None:
                update_fields.append("processed = ?")
                params.append(processed)
            
            if order_ticket is not None:
                update_fields.append("order_ticket = ?")
                params.append(order_ticket)
            
            if order_result is not None:
                update_fields.append("order_result = ?")
                params.append(order_result)
            
            if process_status is not None:
                update_fields.append("process_status = ?")
                params.append(process_status)
            
            if failure_reason is not None:
                update_fields.append("failure_reason = ?")
                params.append(failure_reason)
            
            if update_fields:
                params.append(signal_id)
                query = f"UPDATE signals SET {', '.join(update_fields)} WHERE id = ?"
                c.execute(query, params)
                conn.commit()
                logger.info(f"信号状态更新成功: ID {signal_id}")
            
            conn.close()
    except Exception as e:
        logger.error(f"更新信号状态失败: {e}")

def check_for_signals():
    """检查待处理的信号"""
    try:
        with DB_LOCK:
            conn = sqlite3.connect('trading_data.db')
            c = conn.cursor()
            
            # 查找未处理的信号
            c.execute('''
                SELECT id, timestamp, trading_pair, signal_type, close_price, webhook_data
                FROM signals 
                WHERE processed = 0 OR processed IS NULL
                ORDER BY timestamp ASC
                LIMIT 1
            ''')
            
            signal = c.fetchone()
            conn.close()
            
            if signal:
                signal_dict = {
                    'id': signal[0],
                    'timestamp': signal[1],
                    'trading_pair': signal[2],
                    'signal_type': signal[3],
                    'close_price': signal[4],
                    'webhook_data': signal[5]
                }
                
                logger.info(f"发现待处理信号: {signal_dict}")
                execute_trade(signal_dict)
                
    except Exception as e:
        logger.error(f"检查信号失败: {e}")

def main():
    """主函数"""
    logger.info("MT5交易模块启动 (使用mt5linux)")
    
    # 加载配置
    if not load_config():
        logger.error("加载配置失败，程序退出")
        sys.exit(1)
    
    # 初始化MT5
    if not init_mt5():
        logger.error("初始化MT5失败，程序退出")
        sys.exit(1)
    
    # 主循环
    try:
        while True:
            # 检查信号
            check_for_signals()
            
            # 休眠
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("收到终止信号，程序退出")
    except Exception as e:
        logger.error(f"程序异常: {e}", exc_info=True)
    finally:
        # 关闭MT5连接
        if mt5_initialized and mt5:
            mt5.shutdown()
            logger.info("已关闭MT5连接")

if __name__ == "__main__":
    main()
