#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web界面模块 - VPS2的管理后台
"""

import os, sys, logging, json, sqlite3, time, threading
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, Response
import MetaTrader5 as mt5
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import mt5_trader

# 日志配置
os.makedirs('logs', exist_ok=True)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
file_handler = logging.FileHandler('logs/web_interface.log', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 载入配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    WEB_PORT = config.get('web_port', 8080)
    ADMIN_USERNAME = config.get('admin_username', 'admin')
    ADMIN_PASSWORD = config.get('admin_password', '668899asd')
    
    # 配置日志级别
    log_level = config.get('log_level', 'DEBUG')
    numeric_level = getattr(logging, log_level.upper(), None)
    if isinstance(numeric_level, int):
        logging.getLogger().setLevel(numeric_level)
    
    logger.info(f"配置加载成功，Web端口: {WEB_PORT}")
except Exception as e:
    logger.error(f"加载配置文件失败: {e}", exc_info=True)
    WEB_PORT = 8080
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = '668899asd'

app = Flask(__name__)
app.secret_key = os.urandom(24)
app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24小时会话有效期

# 数据库连接
def get_db_connection():
    """获取数据库连接"""
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row  # 以字典形式返回结果
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}", exc_info=True)
        raise

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 路由: 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session['user_id'] = username
            return redirect(url_for('index'))
        else:
            flash('用户名或密码不正确', 'danger')
    
    return render_template('login.html')

# 路由: 登出
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

# 路由: 首页/控制面板
@app.route('/')
@login_required
def index():
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取最近的信号
        c.execute('''
            SELECT id, timestamp, trading_pair, signal_type, processed, order_ticket, order_result
            FROM signals ORDER BY timestamp DESC LIMIT 10
        ''')
        signals = c.fetchall()
        
        # 获取活跃订单 - 同时从MT5获取所有持仓
        c.execute('''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, sl, tp, status, profit
            FROM orders WHERE status IN ('open', 'partially_closed')
            ORDER BY timestamp DESC
        ''')
        db_active_orders = c.fetchall()
        
        # 从MT5获取所有活跃持仓
        mt5_positions = []
        if mt5.terminal_info() is not None:  # 检查MT5是否连接
            try:
                mt5_positions = mt5_trader.get_all_positions()
                logger.info(f"从MT5获取到 {len(mt5_positions)} 个活跃持仓")
            except Exception as e:
                logger.error(f"获取MT5持仓失败: {e}")
        
        # 合并数据库订单和MT5持仓，确保不重复
        active_orders = []
        db_tickets = set()
        
        # 添加数据库中的订单
        for order in db_active_orders:
            db_tickets.add(order['ticket'])
            active_orders.append(dict(order))
        
        # 添加MT5中的持仓（如果不在数据库中）
        for position in mt5_positions:
            if position['ticket'] not in db_tickets:
                # 转换格式以匹配模板期望的格式
                formatted_position = {
                    'id': None,  # 数据库ID为空
                    'timestamp': position['time'],
                    'ticket': position['ticket'],
                    'trading_pair': position['symbol'],
                    'operation': position['type_str'],
                    'volume': position['volume'],
                    'price': position['price_open'],
                    'sl': position['sl'],
                    'tp': position['tp'],
                    'status': 'open',
                    'profit': position['profit'],
                    'from_mt5': True  # 标记为直接从MT5获取
                }
                active_orders.append(formatted_position)
        
        # 获取最近的已平仓订单
        c.execute('''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, close_price, 
                   profit, status, close_time
            FROM orders WHERE status = 'closed' ORDER BY close_time DESC LIMIT 10
        ''')
        closed_orders = c.fetchall()
        
        # 获取交易统计
        c.execute('''
            SELECT COUNT(*) as total_signals,
                   SUM(CASE WHEN processed = 1 THEN 1 ELSE 0 END) as processed_signals,
                   SUM(CASE WHEN order_ticket IS NOT NULL THEN 1 ELSE 0 END) as executed_signals
            FROM signals
        ''')
        signal_stats = c.fetchone()
        
        c.execute('''
            SELECT COUNT(*) as total_orders,
                   SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_orders,
                   SUM(CASE WHEN status IN ('open', 'partially_closed') THEN 1 ELSE 0 END) as active_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN 1 ELSE 0 END) as loss_orders,
                   SUM(CASE WHEN profit IS NOT NULL THEN profit ELSE 0 END) as total_profit
            FROM orders
        ''')
        order_stats = c.fetchone()
        
        # 处理None值
        if order_stats:
            order_stats = dict(order_stats)
            for key in order_stats:
                if order_stats[key] is None:
                    order_stats[key] = 0
        
        conn.close()
        
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取交易开关状态
        trading_enabled = config.get('enable_trading', False)
        
        # 强制初始化MT5连接
        mt5_connection_initialized = mt5_trader.init_mt5()
        logger.info(f"MT5连接初始化结果: {mt5_connection_initialized}")
        
        # 检查MT5连接状态
        try:
            terminal_info = mt5.terminal_info()
            mt5_connected = terminal_info is not None
            if mt5_connected:
                logger.info(f"MT5已连接，终端名称: {terminal_info.name}")
            else:
                logger.warning("MT5未连接，terminal_info返回None")
        except Exception as e:
            logger.error(f"检查MT5连接状态时出错: {e}")
            mt5_connected = False
            
        logger.info(f"MT5连接状态: {'已连接' if mt5_connected else '未连接'}")
        
        # 获取账户信息
        account_info = mt5_trader.get_account_info() if mt5_connected else {
            'balance': 0.0,
            'equity': 0.0,
            'profit': 0.0,
            'margin': 0.0,
            'free_margin': 0.0,
            'margin_level': 0.0,
            'currency': 'USD'
        }
        logger.info(f"获取账户信息: {account_info}")
        
        return render_template('index.html',
                              signals=signals,
                              active_orders=active_orders,
                              closed_orders=closed_orders,
                              signal_stats=signal_stats,
                              order_stats=order_stats,
                              trading_enabled=trading_enabled,
                              mt5_connected=mt5_connected,
                              account_info=account_info)
    
    except Exception as e:
        logger.error(f"加载首页数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        # Provide defaults so template has required variables
        return render_template('index.html',
                              signals=[],
                              active_orders=[],
                              closed_orders=[],
                              signal_stats={'total_signals': 0, 'processed_signals': 0, 'executed_signals': 0},
                              order_stats={'total_orders': 0, 'closed_orders': 0, 'active_orders': 0, 'profitable_orders': 0, 'loss_orders': 0, 'total_profit': 0},
                              trading_enabled=False,
                              mt5_connected=False,
                              account_info={
                                  'balance': 0.0,
                                  'equity': 0.0, 
                                  'profit': 0.0,
                                  'margin': 0.0,
                                  'free_margin': 0.0,
                                  'margin_level': 0.0,
                                  'currency': 'USD'
                              })

# 路由: 信号管理
@app.route('/signals')
@login_required
def signals():
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        offset = (page - 1) * per_page
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取总条数
        c.execute('SELECT COUNT(*) FROM signals')
        total = c.fetchone()[0]
        
        # 获取分页数据
        c.execute('''
            SELECT id, timestamp, trading_pair, signal_type, interval, mrc_event, processed, 
                   process_time, order_ticket, order_result
            FROM signals ORDER BY timestamp DESC LIMIT ? OFFSET ?
        ''', (per_page, offset))
        signals = c.fetchall()
        
        conn.close()
        
        # 计算总页数
        total_pages = (total + per_page - 1) // per_page
        
        return render_template('signals.html',
                              signals=signals,
                              page=page,
                              per_page=per_page,
                              total=total,
                              total_pages=total_pages)
    
    except Exception as e:
        logger.error(f"加载信号数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('signals.html', signals=[])

# 路由: 信号详情
@app.route('/signal/<int:signal_id>')
@login_required
def signal_detail(signal_id):
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        c.execute('SELECT * FROM signals WHERE id = ?', (signal_id,))
        signal = c.fetchone()
        
        if signal:
            # 获取关联订单
            c.execute('SELECT * FROM orders WHERE signal_id = ?', (signal_id,))
            order = c.fetchone()
        else:
            order = None
        
        conn.close()
        
        if not signal:
            flash('信号不存在', 'danger')
            return redirect(url_for('signals'))
        
        return render_template('signal_detail.html', signal=signal, order=order)
    
    except Exception as e:
        logger.error(f"加载信号详情失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return redirect(url_for('signals'))

# 路由: 订单管理
@app.route('/orders')
@login_required
def orders():
    try:
        # 获取分页和筛选参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', 'all')
        offset = (page - 1) * per_page
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 根据状态筛选
        where_clause = ""
        if status != 'all':
            where_clause = f"WHERE status = '{status}'"
        
        # 获取总条数
        c.execute(f'SELECT COUNT(*) FROM orders {where_clause}')
        total = c.fetchone()[0]
        
        # 获取分页数据
        c.execute(f'''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, sl, tp, 
                   status, profit, close_price, close_time, signal_id
            FROM orders {where_clause} ORDER BY timestamp DESC LIMIT ? OFFSET ?
        ''', (per_page, offset))
        orders = c.fetchall()
        
        conn.close()
        
        # 计算总页数
        total_pages = (total + per_page - 1) // per_page
        
        return render_template('orders.html',
                              orders=orders,
                              page=page,
                              per_page=per_page,
                              total=total,
                              total_pages=total_pages,
                              status=status)
    
    except Exception as e:
        logger.error(f"加载订单数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('orders.html', orders=[])

# 路由: 订单详情
@app.route('/order/<int:order_id>')
@login_required
def order_detail(order_id):
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        c.execute('SELECT * FROM orders WHERE id = ?', (order_id,))
        order = c.fetchone()
        
        if order:
            # 获取关联信号
            c.execute('SELECT * FROM signals WHERE id = ?', (order['signal_id'],))
            signal = c.fetchone()
        else:
            signal = None
        
        conn.close()
        
        if not order:
            flash('订单不存在', 'danger')
            return redirect(url_for('orders'))
        
        return render_template('order_detail.html', order=order, signal=signal)
    
    except Exception as e:
        logger.error(f"加载订单详情失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return redirect(url_for('orders'))

# 路由: 平仓操作
@app.route('/close_position', methods=['POST'])
@login_required
def close_position():
    try:
        ticket = request.form.get('ticket')
        if not ticket:
            return jsonify({'success': False, 'message': '订单号不能为空'})
        
        ticket = int(ticket)
        
        # 检查是否部分平仓
        volume = request.form.get('volume')
        if volume:
            volume = float(volume)
        else:
            volume = None
        
        # 执行平仓
        result = mt5_trader.close_position(ticket, volume)
        
        if result:
            return jsonify({'success': True, 'message': '平仓成功'})
        else:
            return jsonify({'success': False, 'message': '平仓失败，请查看日志'})
    
    except Exception as e:
        logger.error(f"平仓操作失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'平仓操作异常: {str(e)}'})

# 路由: 修改止损止盈
@app.route('/modify_sl_tp', methods=['POST'])
@login_required
def modify_sl_tp():
    try:
        ticket = request.form.get('ticket')
        sl = request.form.get('sl')
        tp = request.form.get('tp')
        
        if not ticket:
            return jsonify({'success': False, 'message': '订单号不能为空'})
        
        ticket = int(ticket)
        sl = float(sl) if sl else None
        tp = float(tp) if tp else None
        
        # 执行修改
        result = mt5_trader.modify_sl_tp(ticket, sl, tp)
        
        if result:
            return jsonify({'success': True, 'message': '修改成功'})
        else:
            return jsonify({'success': False, 'message': '修改失败，请查看日志'})
    
    except Exception as e:
        logger.error(f"修改止损止盈失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'修改操作异常: {str(e)}'})
        
# 路由: 手动执行信号
@app.route('/manual_process_signal', methods=['POST'])
@login_required
def manual_process_signal():
    try:
        signal_id = request.form.get('signal_id')
        
        if not signal_id:
            return jsonify({'success': False, 'message': '信号ID不能为空'})
        
        signal_id = int(signal_id)
        
        # 获取信号信息
        conn = get_db_connection()
        c = conn.cursor()
        c.execute('SELECT * FROM signals WHERE id = ?', (signal_id,))
        signal = c.fetchone()
        
        if not signal:
            conn.close()
            return jsonify({'success': False, 'message': '信号不存在'})
        
        if signal['processed'] == 1:
            conn.close()
            return jsonify({'success': False, 'message': '该信号已经处理过'})
        
        # 从配置文件读取交易启用状态
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if not config.get('enable_trading', False):
            conn.close()
            return jsonify({'success': False, 'message': '交易功能已关闭，请先在设置中开启'})
        
        # 调用MT5交易模块执行信号
        # 直接使用信号数据字典调用execute_trade函数
        signal_dict = dict(signal)
        logger.info(f"尝试执行信号 ID: {signal_id}, 交易对: {signal_dict.get('trading_pair')}, 类型: {signal_dict.get('signal_type')}")
        
        # 执行交易
        result = mt5_trader.execute_trade(signal_dict)
        logger.info(f"信号 ID: {signal_id} 执行结果: {result}")
        
        # 更新信号处理状态
        if result:
            c.execute('''
                UPDATE signals 
                SET processed = 1, process_time = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), signal_id))
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': '信号执行成功'})
        else:
            c.execute('''
                UPDATE signals 
                SET processed = 1, process_time = ?, order_result = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), '交易执行失败', signal_id))
            conn.commit()
            conn.close()
            return jsonify({'success': False, 'message': '交易执行失败，请查看日志了解详情'})
    
    except Exception as e:
        logger.error(f"手动执行信号失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'执行异常: {str(e)}'})

# 路由: 添加手动信号
@app.route('/add_signal', methods=['POST'])
@login_required
def add_signal():
    try:
        trading_pair = request.form.get('trading_pair')
        signal_type = request.form.get('signal_type')
        price = float(request.form.get('price', 0))
        sl = float(request.form.get('sl')) if request.form.get('sl') else None
        tp = float(request.form.get('tp')) if request.form.get('tp') else None
        volume = float(request.form.get('volume')) if request.form.get('volume') else None
        # 可根据需求添加其他字段
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取信号表结构以便检查可用字段
        c.execute("PRAGMA table_info(signals)")
        columns = [column[1] for column in c.fetchall()]
        
        # 根据表结构构建插入查询
        fields = ['timestamp', 'trading_pair', 'signal_type', 'processed']
        values = [datetime.now().isoformat(), trading_pair, signal_type, 0]
        
        # 添加可能的额外字段
        if 'price' in columns and price is not None:
            fields.append('close_price')  # 假设交易价格存储在close_price字段中
            values.append(price)
        
        if 'sl' in columns and sl is not None:
            fields.append('s1')  # 假设止损价格存储在s1字段中
            values.append(sl)
        
        if 'tp' in columns and tp is not None:
            fields.append('r1')  # 假设止盈价格存储在r1字段中
            values.append(tp)
        
        if 'volume' in columns and volume is not None:
            fields.append('volume')
            values.append(volume)
            
        # 添加空值字段
        fields.extend(['order_ticket', 'order_result', 'interval', 'mrc_event'])
        values.extend([None, None, None, None])
        
        # 构建并执行SQL
        placeholders = ', '.join(['?' for _ in values])
        query = f"INSERT INTO signals ({', '.join(fields)}) VALUES ({placeholders})"
        
        logger.info(f"添加手动信号，SQL: {query}")
        logger.info(f"添加手动信号，参数: {values}")
        
        c.execute(query, values)
        signal_id = c.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"成功添加手动信号，ID: {signal_id}")
        flash('手动信号已添加', 'success')
    except Exception as e:
        logger.error(f"添加手动信号失败: {e}", exc_info=True)
        flash('添加手动信号失败', 'danger')
    return redirect(url_for('signals'))

# 路由: 系统设置
@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if request.method == 'POST':
        try:
            # 读取当前配置
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 更新交易开关
            enable_trading = request.form.get('enable_trading') == 'on'
            config['enable_trading'] = enable_trading
            
            # 更新交易量
            default_volume = request.form.get('default_volume')
            if default_volume:
                config['default_volume'] = float(default_volume)
            
            # 更新止损止盈点数
            default_sl_points = request.form.get('default_sl_points')
            if default_sl_points:
                config['default_sl_points'] = int(default_sl_points)
                
            default_tp_points = request.form.get('default_tp_points')
            if default_tp_points:
                config['default_tp_points'] = int(default_tp_points)
            
            # 更新交易对特定配置
            for symbol in ['BTCUSD', 'ETHUSD', 'XAUUSD', 'GBPJPY']:
                symbol_volume = request.form.get(f'{symbol}_volume')
                if symbol_volume:
                    config['symbol_volumes'][symbol] = float(symbol_volume)
                
                symbol_sl = request.form.get(f'{symbol}_sl')
                if symbol_sl:
                    config['symbol_sl_points'][symbol] = int(symbol_sl)
                
                symbol_tp = request.form.get(f'{symbol}_tp')
                if symbol_tp:
                    config['symbol_tp_points'][symbol] = int(symbol_tp)
            
            # 更新Bark通知配置
            bark_device_key = request.form.get('bark_device_key')
            if bark_device_key:
                config['bark_device_key'] = bark_device_key
            
            bark_sound = request.form.get('bark_sound')
            if bark_sound:
                config['bark_sound'] = bark_sound
            
            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            flash('设置已保存', 'success')
            
            # 触发MT5模块重新加载配置
            threading.Thread(target=mt5_trader.load_config).start()
            
            return redirect(url_for('settings'))
        
        except Exception as e:
            logger.error(f"保存设置失败: {e}", exc_info=True)
            flash(f'保存设置失败: {str(e)}', 'danger')
    
    # 读取当前配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        return render_template('settings.html', config=config)
    
    except Exception as e:
        logger.error(f"加载设置页面失败: {e}", exc_info=True)
        flash('加载设置失败，请稍后重试', 'danger')
        return render_template('settings.html', config={})

# 路由: 交易报告
@app.route('/reports')
@login_required
def reports():
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取总体统计
        c.execute('''
            SELECT COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN 1 ELSE 0 END) as loss_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN profit ELSE 0 END) as total_profit,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN profit ELSE 0 END) as total_loss,
                   SUM(profit) as net_profit
            FROM orders WHERE status = 'closed'
        ''')
        overall_stats_row = c.fetchone()
        
        overall_stats = {
            'total_orders': 0, 'profitable_orders': 0, 'loss_orders': 0, 
            'total_profit': 0.0, 'total_loss': 0.0, 'net_profit': 0.0
        }
        if overall_stats_row:
            overall_stats['total_orders'] = overall_stats_row['total_orders'] if overall_stats_row['total_orders'] is not None else 0
            overall_stats['profitable_orders'] = overall_stats_row['profitable_orders'] if overall_stats_row['profitable_orders'] is not None else 0
            overall_stats['loss_orders'] = overall_stats_row['loss_orders'] if overall_stats_row['loss_orders'] is not None else 0
            overall_stats['total_profit'] = overall_stats_row['total_profit'] if overall_stats_row['total_profit'] is not None else 0.0
            overall_stats['total_loss'] = overall_stats_row['total_loss'] if overall_stats_row['total_loss'] is not None else 0.0
            overall_stats['net_profit'] = overall_stats_row['net_profit'] if overall_stats_row['net_profit'] is not None else 0.0
        
        # 按交易对统计
        c.execute('''
            SELECT trading_pair,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders WHERE status = 'closed'
            GROUP BY trading_pair
        ''')
        symbol_stats_rows = c.fetchall()
        symbol_stats = []
        for row in symbol_stats_rows:
            symbol_stats.append({
                'trading_pair': row['trading_pair'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })
        
        # 按交易类型统计
        c.execute('''
            SELECT operation,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders WHERE status = 'closed'
            GROUP BY operation
        ''')
        operation_stats_rows = c.fetchall()
        operation_stats = []
        for row in operation_stats_rows:
            operation_stats.append({
                'operation': row['operation'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })
        
        # 最近的订单
        c.execute('''
            SELECT id, timestamp, trading_pair, operation, volume, profit, status
            FROM orders WHERE status = 'closed'
            ORDER BY timestamp DESC LIMIT 10
        ''')
        recent_orders = c.fetchall()
        recent_orders_list = [dict(order) for order in recent_orders]
        
        conn.close()
        
        # 获取MT5历史交易记录
        mt5_initialized = mt5_trader.init_mt5()
        mt5_history_deals = []
        mt5_history_orders = []
        
        if mt5_initialized:
            try:
                # 从MT5获取历史交易记录和订单记录
                mt5_history_deals = mt5_trader.get_history_deals(mt5_initialized=mt5_initialized, init_mt5=mt5_trader.init_mt5)
                mt5_history_orders = mt5_trader.get_history_orders(mt5_initialized=mt5_initialized, init_mt5=mt5_trader.init_mt5)
                
                logger.info(f"从MT5获取到 {len(mt5_history_deals)} 条历史交易记录和 {len(mt5_history_orders)} 条历史订单记录")
                
                # 如果获取到MT5历史记录，更新总体统计信息
                if mt5_history_deals:
                    mt5_profit = sum(deal['profit'] for deal in mt5_history_deals if deal['profit'] is not None)
                    mt5_profitable_deals = sum(1 for deal in mt5_history_deals if deal['profit'] is not None and deal['profit'] > 0)
                    mt5_loss_deals = sum(1 for deal in mt5_history_deals if deal['profit'] is not None and deal['profit'] < 0)
                    
                    # 合并统计数据（将数据库和MT5的数据合并）
                    overall_stats['total_orders'] += len(mt5_history_deals)
                    overall_stats['profitable_orders'] += mt5_profitable_deals
                    overall_stats['loss_orders'] += mt5_loss_deals
                    overall_stats['net_profit'] += mt5_profit
                    
                    # 添加最近的MT5交易记录到展示列表（只添加有利润的交易记录，排除余额操作等）
                    recent_mt5_deals = [
                        {
                            'id': deal['deal'],  # 使用MT5交易ID
                            'timestamp': deal['time'],
                            'trading_pair': deal['symbol'],
                            'operation': 'buy' if deal['type'] == mt5.DEAL_TYPE_BUY else 'sell' if deal['type'] == mt5.DEAL_TYPE_SELL else 'other',
                            'volume': deal['volume'],
                            'profit': deal['profit'],
                            'status': 'closed',
                            'from_mt5': True  # 标记为来自MT5的数据
                        }
                        for deal in mt5_history_deals
                        if deal['profit'] is not None and deal['type'] in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL]
                    ]
                    
                    # 合并并按时间排序
                    all_recent_orders = recent_orders_list + recent_mt5_deals
                    all_recent_orders.sort(key=lambda x: x['timestamp'], reverse=True)
                    recent_orders = all_recent_orders[:10]  # 只取前10条
            except Exception as e:
                logger.error(f"获取MT5历史记录时出错: {e}", exc_info=True)
        
        return render_template('reports.html',
                              overall_stats=overall_stats,
                              symbol_stats=symbol_stats,
                              operation_stats=operation_stats,
                              recent_orders=recent_orders,
                              mt5_history_deals=mt5_history_deals,
                              mt5_history_orders=mt5_history_orders)
    
    except Exception as e:
        logger.error(f"加载报告数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('reports.html',
                               overall_stats={'total_orders': 0, 'profitable_orders': 0, 'loss_orders': 0, 'total_profit': 0.0, 'total_loss': 0.0, 'net_profit': 0.0},
                               symbol_stats=[],
                               operation_stats=[],
                               recent_orders=[],
                               mt5_history_deals=[],
                               mt5_history_orders=[])

# 路由: API - 获取系统状态
@app.route('/api/status')
def api_status():
    try:
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取交易开关状态
        trading_enabled = config.get('enable_trading', False)
        
        # 检查MT5连接状态
        mt5_connected = mt5.terminal_info() is not None
        
        return jsonify({
            'success': True,
            'trading_enabled': trading_enabled,
            'mt5_connected': mt5_connected,
            'server_time': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取账户信息
@app.route('/api/account_info')
@login_required
def api_account_info():
    try:
        # 强制初始化MT5连接
        mt5_connection_initialized = mt5_trader.init_mt5()
        logger.info(f"API - MT5连接初始化结果: {mt5_connection_initialized}")
        
        # 检查MT5连接状态
        try:
            terminal_info = mt5.terminal_info()
            mt5_connected = terminal_info is not None
            if mt5_connected:
                logger.info(f"API - MT5已连接，终端名称: {terminal_info.name}")
            else:
                logger.warning("API - MT5未连接，terminal_info返回None")
        except Exception as e:
            logger.error(f"API - 检查MT5连接状态时出错: {e}")
            mt5_connected = False
            
        logger.info(f"API - MT5连接状态: {'已连接' if mt5_connected else '未连接'}")
        
        # 获取账户信息
        account_info = mt5_trader.get_account_info() if mt5_connected else {
            'balance': 0.0,
            'equity': 0.0,
            'profit': 0.0,
            'margin': 0.0,
            'free_margin': 0.0,
            'margin_level': 0.0,
            'currency': 'USD'
        }
        logger.info(f"API - 获取账户信息: {account_info}")
        
        return jsonify({
            'success': True,
            'mt5_connected': mt5_connected,
            'account_info': account_info
        })
    except Exception as e:
        logger.error(f"获取账户信息失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取实时价格
@app.route('/api/prices', methods=['GET'])
@login_required
def get_real_time_prices_api():
    """获取实时价格的API"""
    try:
        # 要监控的交易品种
        symbols = ["XAUUSD", "BTCUSD", "ETHUSD", "GBPJPY"]
        
        # 先检查MT5是否连接
        mt5_connected = False
        try:
            mt5_connected = mt5.terminal_info() is not None
            if not mt5_connected:
                # 尝试初始化连接
                mt5_connected = mt5_trader.init_mt5()
        except Exception as e:
            logger.error(f"检查MT5连接失败: {e}", exc_info=True)
        
        if not mt5_connected:
            # MT5未连接，返回错误信息
            return jsonify({
                "status": "success",  # 仍然返回成功状态码，但包含错误信息
                "data": {
                    "XAUUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "BTCUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "ETHUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "GBPJPY": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"}
                },
                "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 获取实时价格
        prices = mt5_trader.get_real_time_prices(symbols)
        return jsonify({"status": "success", "data": prices, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
    except Exception as e:
        logger.error(f"获取实时价格时出错: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)})

# 路由: API - 获取盈亏历史数据用于图表
@app.route('/api/profit_history')
@login_required
def api_profit_history():
    try:
        # 获取时间段参数
        period = request.args.get('period', 'all')
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 根据时间段筛选
        where_clause = ""
        if period == 'today':
            where_clause = "WHERE DATE(timestamp) = DATE('now')"
        elif period == 'week':
            where_clause = "WHERE DATE(timestamp) >= DATE('now', '-7 days')"
        elif period == 'month':
            where_clause = "WHERE DATE(timestamp) >= DATE('now', '-30 days')"
        
        # 获取已平仓订单
        c.execute(f'''
            SELECT timestamp, profit 
            FROM orders 
            {where_clause} AND status = 'closed'
            ORDER BY timestamp ASC
        ''')
        
        orders = c.fetchall()
        
        conn.close()
        
        # 按时间累计盈亏
        dates = []
        profits = []
        accumulated_profit = 0
        
        for order in orders:
            dates.append(order['timestamp'].split('T')[0])  # 只取日期部分
            accumulated_profit += order['profit']
            profits.append(accumulated_profit)
        
        # 如果没有数据，提供默认值
        if not dates:
            dates = [datetime.now().strftime('%Y-%m-%d')]
            profits = [0]
        
        return jsonify({
            'success': True,
            'dates': dates,
            'profits': profits
        })
    except Exception as e:
        logger.error(f"获取盈亏历史数据失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: 导出报告
@app.route('/export_report')
@login_required
def export_report():
    try:
        import csv
        import io
        from flask import Response
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取所有已平仓订单
        c.execute('''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, close_price,
                   profit, close_time, signal_id
            FROM orders WHERE status = 'closed'
            ORDER BY timestamp DESC
        ''')
        
        orders = c.fetchall()
        conn.close()
        
        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['订单ID', '开仓时间', '订单号', '交易对', '交易方向', '交易量', 
                        '开仓价', '平仓价', '盈亏', '平仓时间', '关联信号ID'])
        
        # 写入数据
        for order in orders:
            row = [
                order['id'],
                order['timestamp'].replace('T', ' ').split('.')[0],
                order['ticket'],
                order['trading_pair'],
                '买入' if order['operation'] == 'buy' else '卖出',
                order['volume'],
                order['price'],
                order['close_price'],
                order['profit'],
                order['close_time'].replace('T', ' ').split('.')[0] if order['close_time'] else '-',
                order['signal_id']
            ]
            writer.writerow(row)
        
        # 设置响应头，使浏览器将其作为文件下载
        output.seek(0)
        date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        return Response(
            output,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment;filename=trading_report_{date_str}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )
    
    except Exception as e:
        logger.error(f"导出报告失败: {e}", exc_info=True)
        flash('导出报告失败，请稍后重试', 'danger')
        return redirect(url_for('reports'))

# 路由: 导出信号CSV
@app.route('/export_signals_csv')
@login_required
def export_signals_csv():
    try:
        import csv
        import io
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取所有信号
        c.execute('''
            SELECT id, timestamp, trading_pair, signal_type, interval, mrc_event, processed, 
                   process_time, order_ticket, order_result
            FROM signals ORDER BY timestamp DESC
        ''')
        
        signals = c.fetchall()
        conn.close()
        
        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['信号ID', '时间戳', '交易对', '信号类型', '时间周期', 'MRC事件', 
                        '是否已处理', '处理时间', '订单号', '订单结果'])
        
        # 写入数据
        for signal_row in signals:  # Renamed to avoid conflict
            row = [
                signal_row['id'],
                signal_row['timestamp'].replace('T', ' ').split('.')[0],
                signal_row['trading_pair'],
                signal_row['signal_type'],
                signal_row['interval'],
                signal_row['mrc_event'],
                '是' if signal_row['processed'] == 1 else '否',
                signal_row['process_time'].replace('T', ' ').split('.')[0] if signal_row['process_time'] else '-',
                signal_row['order_ticket'],
                signal_row['order_result']
            ]
            writer.writerow(row)
        
        # 设置响应头，使浏览器将其作为文件下载
        output.seek(0)
        date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        return Response(
            output,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment;filename=signals_export_{date_str}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )
    
    except Exception as e:
        logger.error(f"导出信号CSV失败: {e}", exc_info=True)
        flash('导出信号CSV失败，请稍后重试', 'danger')
        return redirect(url_for('signals'))

# 路由: API - 获取实时价格
@app.route('/api/prices', methods=['GET'])
@login_required
def get_real_time_prices_api():
    """获取实时价格的API"""
    try:
        # 要监控的交易品种
        symbols = ["XAUUSD", "BTCUSD", "ETHUSD", "GBPJPY"]
        
        # 先检查MT5是否连接
        mt5_connected = False
        try:
            mt5_connected = mt5.terminal_info() is not None
            if not mt5_connected:
                # 尝试初始化连接
                mt5_connected = mt5_trader.init_mt5()
        except Exception as e:
            logger.error(f"检查MT5连接失败: {e}", exc_info=True)
        
        if not mt5_connected:
            # MT5未连接，返回错误信息
            return jsonify({
                "status": "success",  # 仍然返回成功状态码，但包含错误信息
                "data": {
                    "XAUUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "BTCUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "ETHUSD": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"},
                    "GBPJPY": {"bid": 0.0, "ask": 0.0, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "spread": 0.0, "error": "MT5未连接"}
                },
                "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 获取实时价格
        prices = mt5_trader.get_real_time_prices(symbols)
        return jsonify({"status": "success", "data": prices, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
    except Exception as e:
        logger.error(f"获取实时价格时出错: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)})

if __name__ == '__main__':
    try:
        logger.info(f"Web界面启动，端口: {WEB_PORT}")
        app.run(host='0.0.0.0', port=WEB_PORT, debug=True)
    except Exception as e:
        logger.error(f"Web界面启动失败: {e}", exc_info=True)
