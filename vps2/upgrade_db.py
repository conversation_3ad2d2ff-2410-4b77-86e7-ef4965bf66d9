#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库升级脚本 - 添加自动平仓规则表
"""

import sqlite3
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def upgrade_database():
    """升级数据库，添加自动平仓规则表"""
    db_path = 'trading_data.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
          # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_close_rules';")
        if cursor.fetchone():
            logger.info("auto_close_rules 表已存在，跳过创建")
            conn.close()
            return True
        
        # 创建自动平仓规则表
        cursor.execute('''
            CREATE TABLE auto_close_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket INTEGER UNIQUE NOT NULL,  -- 订单号
                profit_threshold REAL,           -- 盈利阈值（美元）
                loss_threshold REAL,             -- 亏损阈值（美元）
                created_time TEXT NOT NULL,      -- 创建时间
                updated_time TEXT NOT NULL,      -- 更新时间
                enabled INTEGER DEFAULT 1,       -- 是否启用 (1=启用, 0=禁用)
                FOREIGN KEY (ticket) REFERENCES orders (ticket)
            )
        ''')
        
        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX idx_auto_close_rules_ticket ON auto_close_rules(ticket);')
        cursor.execute('CREATE INDEX idx_auto_close_rules_enabled ON auto_close_rules(enabled);')
        
        conn.commit()
        logger.info("成功创建 auto_close_rules 表")
        
        # 验证表结构
        cursor.execute("PRAGMA table_info(auto_close_rules);")
        columns = cursor.fetchall()
        logger.info("auto_close_rules 表结构:")
        for col in columns:
            logger.info(f"  {col[1]} ({col[2]})")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库升级失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = upgrade_database()
    if success:
        print("数据库升级成功！")
    else:
        print("数据库升级失败！")
