#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VPS2主程序 - 接收交易信号并执行MT5交易
"""
import subprocess
import time
import logging
import os
import json
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    filename='logs/main.log',
                    format='%(asctime)s %(levelname)s:%(message)s')

# 载入配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 配置日志级别
    log_level = config.get('log_level', 'INFO')
    numeric_level = getattr(logging, log_level.upper(), None)
    if isinstance(numeric_level, int):
        logging.getLogger().setLevel(numeric_level)
    
except Exception as e:
    logging.error(f"加载配置文件失败: {e}", exc_info=True)

def start_process(script_name, log_file_base):
    """启动一个Python子进程"""
    try:
        # 使用当前Python解释器启动并记录输出
        stdout_log = open(os.path.join('logs', f'{log_file_base}.out.log'), 'a', encoding='utf-8')
        stderr_log = open(os.path.join('logs', f'{log_file_base}.err.log'), 'a', encoding='utf-8')
        
        env = os.environ.copy()
        python_path = os.environ.get("PYTHONPATH", "")
        # Add the directory of the current Python executable to PYTHONPATH
        # This helps ensure that subprocesses find packages installed in the same environment
        env["PYTHONPATH"] = f"{os.path.dirname(sys.executable)}{os.pathsep}{python_path}"

        process = subprocess.Popen(
            [sys.executable, script_name],
            stdout=stdout_log,
            stderr=stderr_log,
            cwd=os.getcwd(), # Ensure subprocesses run in the main script's CWD
            env=env  # Pass the modified environment
        )
        logging.info(f"启动 {script_name} 进程，PID: {process.pid}")
        return process
    except Exception as e:
        logging.error(f"启动 {script_name} 失败: {e}", exc_info=True)
        return None

def main():
    """主函数，启动所有子系统"""
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    logging.info("===== MT5交易系统启动 =====")
    
    processes = []
    
    # 启动信号接收服务器
    processes.append(start_process('signal_receiver.py', 'signal_receiver'))
    
    # 启动MT5交易模块
    processes.append(start_process('mt5_trader.py', 'mt5_trader'))
    
    # 启动Web界面
    processes.append(start_process('web_interface.py', 'web_interface'))
    
    try:
        # 保持主进程运行，监控子进程
        while True:
            time.sleep(10)
            # 检查进程是否存活，如果不存在则重启
            for i, process in enumerate(processes[:]):
                if process is None:
                    continue
                    
                if process.poll() is not None:  # 如果进程已终止
                    script = ['signal_receiver.py', 'mt5_trader.py', 'web_interface.py'][i]
                    logging.warning(f"{script} 进程已终止，正在重启...")
                    processes[i] = start_process(script, script.split('.')[0])
    
    except KeyboardInterrupt:
        logging.info("接收到终止信号，正在关闭所有进程...")
        for process in processes:
            if process is not None:
                process.terminate()
                process.wait()
        logging.info("所有进程已终止，系统关闭")

if __name__ == '__main__':
    main()
