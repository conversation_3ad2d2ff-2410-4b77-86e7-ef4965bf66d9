#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bark通知模块 - 用于通过Bark发送交易执行通知
"""

import json
import logging
import os
import requests
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

def load_config():
    """载入配置"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}

def is_notification_enabled(notification_type, config=None):
    """
    检查指定类型的通知是否启用

    参数:
        notification_type: 通知类型 ('signal_received', 'signal_processing', 'trade_execution', 'trade_closed', 'error')
        config: 配置对象，如果为None则重新加载

    返回:
        bool: 是否启用该类型的通知
    """
    if not config:
        config = load_config()

    # 获取通知设置，默认全部启用
    bark_notifications = config.get('bark_notifications', {
        'signal_received': True,
        'signal_processing': True,
        'trade_execution': True,
        'trade_closed': True,
        'error': True
    })

    # 余额监测相关通知默认启用
    balance_monitoring = config.get('balance_monitoring', {})
    if notification_type == 'balance_change':
        return balance_monitoring.get('balance_change_notification', {}).get('enabled', True)
    elif notification_type == 'profit_loss':
        return balance_monitoring.get('profit_loss_notification', {}).get('enabled', True)
    elif notification_type == 'periodic_balance':
        return balance_monitoring.get('periodic_balance_notification', {}).get('enabled', True)

    return bark_notifications.get(notification_type, True)

def send_bark_notification(title, body, config=None, notification_type=None, use_device_2=False):
    """
    发送Bark通知（支持多设备）

    参数:
        title: 通知标题
        body: 通知内容
        config: 配置对象，如果为None则重新加载
        notification_type: 通知类型，用于检查是否启用
        use_device_2: 是否只使用第二个设备

    返回:
        bool: 是否至少有一个设备发送成功
    """
    if not config:
        config = load_config()

    # 检查通知类型是否启用
    if notification_type and not is_notification_enabled(notification_type, config):
        logger.info(f"通知类型 '{notification_type}' 已禁用，跳过发送")
        return True  # 返回True表示"成功"跳过

    bark_url = config.get('bark_url', '')
    bark_device_key = config.get('bark_device_key', '')
    bark_device_key_2 = config.get('bark_device_key_2', '')

    if not bark_url:
        logger.warning("Bark URL未配置")
        return False

    # 收集所有有效的设备密钥
    device_keys = []
    if use_device_2:
        # 只使用第二个设备
        if bark_device_key_2:
            device_keys.append(bark_device_key_2)
    else:
        # 使用所有设备
        if bark_device_key:
            device_keys.append(bark_device_key)
        if bark_device_key_2:
            device_keys.append(bark_device_key_2)

    if not device_keys:
        logger.warning("没有配置有效的Bark设备密钥")
        return False

    success_count = 0
    total_count = len(device_keys)

    for i, device_key in enumerate(device_keys, 1):
        try:
            # 构建Bark通知载荷
            payload = {
                'title': title,
                'body': body,
                'device_key': device_key,
                'level': config.get('bark_level', 'active'),
                'badge': int(config.get('bark_badge', 1)),
                'sound': config.get('bark_sound', 'default'),
                'icon': 'https://raw.githubusercontent.com/tickmill-paper/assets/main/logo-m.png',
                'group': 'MT5交易'
            }

            # 添加复制功能
            payload['copy'] = body

            logger.debug(f"发送Bark通知到设备{i}: {json.dumps(payload, ensure_ascii=False)}")

            # 发送请求
            response = requests.post(
                f"{bark_url}/{device_key}",
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"Bark通知发送成功 (设备{i})")
                success_count += 1
            else:
                logger.error(f"Bark通知发送失败 (设备{i}): {response.status_code} - {response.text}")

        except requests.RequestException as e:
            logger.error(f"Bark通知发送网络错误 (设备{i}): {e}")
        except Exception as e:
            logger.error(f"Bark通知发送意外错误 (设备{i}): {e}")

    # 记录总体结果
    if success_count == total_count:
        logger.info(f"所有Bark设备通知发送成功 ({success_count}/{total_count})")
    elif success_count > 0:
        logger.warning(f"部分Bark设备通知发送成功 ({success_count}/{total_count})")
    else:
        logger.error(f"所有Bark设备通知发送失败 (0/{total_count})")

    return success_count > 0

def notify_trade_execution(trade_info):
    """
    发送交易执行通知

    参数:
        trade_info: 交易信息字典
    """
    config = load_config()
    title = config.get('bark_title', 'MT5交易执行')

    # 构建通知内容
    operation = "买入" if trade_info.get('operation') == 'buy' else "卖出"
    symbol = trade_info.get('symbol', 'Unknown')
    volume = trade_info.get('volume', 0)
    price = trade_info.get('price', 0)
    sl = trade_info.get('sl', 0)
    tp = trade_info.get('tp', 0)
    ticket = trade_info.get('ticket', 0)

    body = (
        f"💰 交易对: {symbol}\n"
        f"操作: {operation}\n"
        f"交易量: {volume}\n"
        f"价格: {price}\n"
        f"止损: {sl}\n"
        f"止盈: {tp}\n"
        f"订单号: {ticket}\n"
        f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

    return send_bark_notification(f"{title} - 新订单", body, config, 'trade_execution')

def notify_trade_closed(trade_info):
    """
    发送交易关闭通知
    
    参数:
        trade_info: 交易信息字典
    """
    config = load_config()
    title = config.get('bark_title', 'MT5交易执行')
    
    # 构建通知内容
    operation = "买入平仓" if trade_info.get('operation') == 'buy' else "卖出平仓"
    symbol = trade_info.get('symbol', 'Unknown')
    volume = trade_info.get('volume', 0)
    open_price = trade_info.get('open_price', 0)
    close_price = trade_info.get('close_price', 0)
    profit = trade_info.get('profit', 0)
    ticket = trade_info.get('ticket', 0)
    points = trade_info.get('points', 0)
    
    # 添加盈亏状态和表情
    profit_status = "盈利" if profit > 0 else "亏损"
    emoji = "🔴" if profit < 0 else "🟢"
    
    body = (
        f"{emoji} 交易对: {symbol}\n"
        f"操作: {operation}\n"
        f"交易量: {volume}\n"
        f"开仓价: {open_price}\n"
        f"平仓价: {close_price}\n"
        f"盈亏: {profit} ({profit_status})\n"
        f"盈亏点数: {points}\n"
        f"订单号: {ticket}\n"
        f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )
    
    return send_bark_notification(f"{title} - 平仓 ({profit_status})", body, config, 'trade_closed')

def notify_error(error_message):
    """
    发送错误通知

    参数:
        error_message: 错误信息
    """
    config = load_config()
    title = config.get('bark_title', 'MT5交易执行')

    body = f"错误: {error_message}\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    return send_bark_notification(f"{title} - 错误", body, config, 'error')

def notify_signal_received(signal_info):
    """
    发送信号接收通知

    参数:
        signal_info: 信号信息字典
    """
    config = load_config()
    title = config.get('bark_title', 'MT5交易执行')

    # 构建通知内容
    trading_pair = signal_info.get('trading_pair', 'Unknown')
    signal_type = signal_info.get('signal_type', 'Unknown')
    signal_id = signal_info.get('signal_id', 'Unknown')
    interval = signal_info.get('interval', 'Unknown')
    mrc_event = signal_info.get('mrc_event', 'Unknown')

    operation = "买入信号" if signal_type.lower() == 'buy' else "卖出信号"

    body = (
        f"📡 交易对: {trading_pair}\n"
        f"信号类型: {operation}\n"
        f"时间周期: {interval}\n"
        f"事件类型: {mrc_event}\n"
        f"信号ID: {signal_id}\n"
        f"接收时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

    return send_bark_notification(f"{title} - 信号接收", body, config, 'signal_received')

def notify_signal_processing(signal_info, status, details=None):
    """
    发送信号处理状态通知

    参数:
        signal_info: 信号信息字典
        status: 处理状态 ('started', 'success', 'failed', 'skipped')
        details: 详细信息
    """
    config = load_config()
    title = config.get('bark_title', 'MT5交易执行')

    # 构建通知内容
    trading_pair = signal_info.get('trading_pair', 'Unknown')
    signal_type = signal_info.get('signal_type', 'Unknown')
    signal_id = signal_info.get('id', signal_info.get('signal_id', 'Unknown'))

    operation = "买入信号" if signal_type.lower() == 'buy' else "卖出信号"

    # 根据状态设置不同的标题和内容
    if status == 'started':
        emoji = "⚡"
        status_text = "开始处理"
        title_suffix = "信号处理"
    elif status == 'success':
        emoji = "✅"
        status_text = "处理成功"
        title_suffix = "处理成功"
    elif status == 'failed':
        emoji = "❌"
        status_text = "处理失败"
        title_suffix = "处理失败"
    elif status == 'skipped':
        emoji = "⏭️"
        status_text = "跳过处理"
        title_suffix = "跳过处理"
    else:
        emoji = "ℹ️"
        status_text = status
        title_suffix = "状态更新"

    body = (
        f"{emoji} 交易对: {trading_pair}\n"
        f"信号类型: {operation}\n"
        f"处理状态: {status_text}\n"
        f"信号ID: {signal_id}\n"
    )

    if details:
        body += f"详细信息: {details}\n"

    body += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    return send_bark_notification(f"{title} - {title_suffix}", body, config, 'signal_processing')

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.DEBUG)
    test_trade = {
        'symbol': 'XAUUSD',
        'operation': 'buy',
        'volume': 0.1,
        'price': 1900.50,
        'sl': 1890.00,
        'tp': 1920.00,
        'ticket': 12345678
    }
    
    result = notify_trade_execution(test_trade)
    print(f"通知发送结果: {result}")
