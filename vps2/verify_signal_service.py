#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证信号接收服务状态
"""

import psutil
import socket
import requests
import json

def verify_signal_service():
    """验证信号接收服务状态"""
    print('信号接收服务状态验证')
    print('=' * 40)
    
    # 1. 检查进程
    print('\n1. 进程检查:')
    signal_running = False
    signal_pid = None
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    signal_running = True
                    signal_pid = proc.info['pid']
                    print(f'✅ 信号接收服务正在运行 (PID: {signal_pid})')
                    break
        except:
            pass
    
    if not signal_running:
        print('❌ 信号接收服务未运行')
        return False
    
    # 2. 检查端口
    print('\n2. 端口检查:')
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        server_port = config.get('server_port', 9999)
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', server_port))
        sock.close()
        
        if result == 0:
            print(f'✅ 端口 {server_port} 可访问')
        else:
            print(f'❌ 端口 {server_port} 不可访问')
            return False
            
    except Exception as e:
        print(f'❌ 端口检查失败: {e}')
        return False
    
    # 3. 测试API端点
    print('\n3. API端点测试:')
    
    # 测试主要的交易信号端点
    try:
        test_signal = {
            'trading_pair': 'EURUSD',
            'signal_type': 'test',
            'action': 'test'
        }
        
        response = requests.post(
            f'http://localhost:{server_port}/trade_signal',
            json=test_signal,
            timeout=5
        )
        
        if response.status_code == 200:
            print('✅ /trade_signal 端点响应正常')
            result = response.json()
            print(f'   响应: {result.get("message", "无消息")}')
        else:
            print(f'⚠️ /trade_signal 端点响应异常: {response.status_code}')
            
    except Exception as e:
        print(f'❌ /trade_signal 端点测试失败: {e}')
    
    # 测试健康检查端点（如果存在）
    try:
        response = requests.get(f'http://localhost:{server_port}/health', timeout=5)
        
        if response.status_code == 200:
            print('✅ /health 端点响应正常')
            data = response.json()
            print(f'   状态: {data.get("status")}')
            print(f'   交易开关: {"开启" if data.get("trading_enabled") else "关闭"}')
        else:
            print(f'⚠️ /health 端点不存在或响应异常: {response.status_code}')
            
    except Exception as e:
        print(f'⚠️ /health 端点测试失败: {e}')
    
    # 4. 检查配置
    print('\n4. 配置检查:')
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        server_port = config.get('server_port', 9999)
        enable_trading = config.get('enable_trading', False)
        
        print(f'✅ 服务端口: {server_port}')
        print(f'✅ 交易开关: {"开启" if enable_trading else "关闭"}')
        
    except Exception as e:
        print(f'❌ 配置检查失败: {e}')
    
    print('\n=' * 40)
    print('🎉 信号接收服务验证完成！')
    print(f'服务状态: {"正常运行" if signal_running else "未运行"}')
    print(f'进程ID: {signal_pid}')
    print(f'服务端口: {server_port}')
    
    return True

if __name__ == "__main__":
    verify_signal_service()
