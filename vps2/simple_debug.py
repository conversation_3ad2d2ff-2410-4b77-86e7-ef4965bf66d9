#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的调试脚本
"""

import MetaTrader5 as mt5
import sqlite3
from datetime import datetime, timedelta

def init_mt5():
    """初始化MT5"""
    if not mt5.initialize():
        print("MT5初始化失败")
        return False
    return True

def debug_history():
    """调试历史记录"""
    print("=== 调试历史记录 ===")
    
    if not init_mt5():
        return
    
    # 获取账户信息
    account = mt5.account_info()
    print(f"账户: {account.login}, 服务器: {account.server}")
    
    # 尝试不同时间范围
    ranges = [
        ("1小时", 1/24),
        ("1天", 1),
        ("7天", 7),
        ("30天", 30),
        ("90天", 90)
    ]
    
    for name, days in ranges:
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        print(f"\n{name} ({start_time.strftime('%m-%d %H:%M')} 到 {end_time.strftime('%m-%d %H:%M')}):")
        
        deals = mt5.history_deals_get(start_time, end_time)
        if deals is None:
            print(f"  获取失败，错误: {mt5.last_error()}")
            continue
        
        print(f"  获取到 {len(deals)} 条记录")
        
        if len(deals) > 0:
            # 统计类型
            buy_count = sum(1 for d in deals if d.type == 0)  # DEAL_TYPE_BUY = 0
            sell_count = sum(1 for d in deals if d.type == 1)  # DEAL_TYPE_SELL = 1
            entry_in = sum(1 for d in deals if d.entry == 0)  # DEAL_ENTRY_IN = 0
            entry_out = sum(1 for d in deals if d.entry == 1)  # DEAL_ENTRY_OUT = 1
            
            print(f"    买入: {buy_count}, 卖出: {sell_count}")
            print(f"    入场: {entry_in}, 出场: {entry_out}")
            
            # 显示前3条记录
            print("    前3条记录:")
            for i, deal in enumerate(deals[:3]):
                deal_time = datetime.fromtimestamp(deal.time)
                entry_str = "入场" if deal.entry == 0 else "出场" if deal.entry == 1 else f"其他({deal.entry})"
                type_str = "买入" if deal.type == 0 else "卖出" if deal.type == 1 else f"其他({deal.type})"
                
                print(f"      {i+1}. {deal_time.strftime('%m-%d %H:%M')} | {deal.symbol} | {type_str} | {entry_str} | 盈亏: {deal.profit}")
            
            # 重点：检查出场记录
            if entry_out > 0:
                print(f"    *** 发现 {entry_out} 条出场记录 ***")
                out_deals = [d for d in deals if d.entry == 1]
                for deal in out_deals[:3]:  # 只显示前3条
                    deal_time = datetime.fromtimestamp(deal.time)
                    print(f"      出场: 持仓ID={deal.position_id}, 时间={deal_time.strftime('%m-%d %H:%M')}, 盈亏={deal.profit}")
                
                return out_deals  # 返回出场记录用于进一步分析
    
    return []

def check_database():
    """检查数据库"""
    print("\n=== 检查数据库 ===")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 统计
        c.execute('SELECT status, COUNT(*) FROM orders GROUP BY status')
        for row in c.fetchall():
            print(f"{row[0]}: {row[1]} 个")
        
        # 显示一些开仓订单
        print("\n开仓订单示例:")
        c.execute('SELECT ticket, trading_pair, operation, volume FROM orders WHERE status = "open" LIMIT 5')
        for order in c.fetchall():
            print(f"  票号: {order['ticket']}, 交易对: {order['trading_pair']}, 操作: {order['operation']}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库错误: {e}")

def test_sync_logic(out_deals):
    """测试同步逻辑"""
    if not out_deals:
        print("\n=== 没有出场记录可以测试同步 ===")
        return
    
    print(f"\n=== 测试同步逻辑 ===")
    print(f"找到 {len(out_deals)} 条出场记录")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        for deal in out_deals[:5]:  # 只测试前5条
            position_id = deal.position_id
            print(f"\n处理持仓ID: {position_id}")
            
            # 查找对应订单
            c.execute('SELECT id, ticket, status FROM orders WHERE ticket = ?', (position_id,))
            order = c.fetchone()
            
            if order:
                print(f"  找到订单: ID={order['id']}, 状态={order['status']}")
                if order['status'] != 'closed':
                    print(f"  -> 可以更新为已平仓")
                else:
                    print(f"  -> 已经是已平仓状态")
            else:
                print(f"  未找到对应订单")
        
        conn.close()
        
    except Exception as e:
        print(f"测试同步时出错: {e}")

def main():
    """主函数"""
    print("简化调试脚本")
    print("=" * 40)
    
    # 1. 调试历史记录
    out_deals = debug_history()
    
    # 2. 检查数据库
    check_database()
    
    # 3. 测试同步逻辑
    test_sync_logic(out_deals)
    
    # 4. 总结
    print(f"\n=== 总结 ===")
    if out_deals:
        print(f"✓ 找到 {len(out_deals)} 条出场记录，同步功能应该可以工作")
        print("建议: 运行Web界面的同步功能")
    else:
        print("✗ 没有找到出场记录")
        print("可能原因:")
        print("  1. MT5账户没有平仓的交易历史")
        print("  2. 所有交易都还在持仓中")
        print("  3. 历史记录时间范围不够")
        
        # 检查当前持仓
        if init_mt5():
            positions = mt5.positions_get()
            if positions:
                print(f"  当前有 {len(positions)} 个持仓")
                print("  建议: 手动平仓一个持仓来测试同步功能")
            else:
                print("  当前没有持仓")

if __name__ == "__main__":
    main()
