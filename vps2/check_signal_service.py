#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查和修复信号接收服务
"""

import psutil
import subprocess
import os
import time
import json
import socket

def check_signal_receiver_running():
    """检查信号接收服务是否运行"""
    print('=== 检查信号接收服务状态 ===')
    
    # 方法1：检查进程
    signal_running = False
    signal_pid = None
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    signal_running = True
                    signal_pid = proc.info['pid']
                    print(f'✅ 信号接收服务正在运行 (PID: {signal_pid})')
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if not signal_running:
        print('❌ 信号接收服务未运行')
    
    # 方法2：检查端口
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        server_port = config.get('server_port', 9999)
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', server_port))
        sock.close()
        
        if result == 0:
            print(f'✅ 信号接收服务端口 {server_port} 可访问')
            port_accessible = True
        else:
            print(f'❌ 信号接收服务端口 {server_port} 不可访问')
            port_accessible = False
            
    except Exception as e:
        print(f'❌ 检查端口失败: {e}')
        port_accessible = False
    
    # 方法3：检查日志文件
    log_path = "logs/signal_receiver.log"
    if os.path.exists(log_path):
        last_modified_time = os.path.getmtime(log_path)
        current_time = time.time()
        time_diff = current_time - last_modified_time
        
        if time_diff < 600:  # 10分钟内
            print(f'✅ 信号接收服务日志文件最近更新 ({time_diff:.1f}秒前)')
            log_recent = True
        else:
            print(f'⚠️ 信号接收服务日志文件较旧 ({time_diff/60:.1f}分钟前)')
            log_recent = False
    else:
        print('❌ 信号接收服务日志文件不存在')
        log_recent = False
    
    return signal_running, signal_pid, port_accessible, log_recent

def start_signal_receiver():
    """启动信号接收服务"""
    print('\n=== 启动信号接收服务 ===')
    
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 启动服务
        process = subprocess.Popen(
            ['python', 'signal_receiver.py'],
            stdout=open('logs/signal_receiver.out.log', 'a'),
            stderr=open('logs/signal_receiver.err.log', 'a'),
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        
        print(f'✅ 信号接收服务启动成功 (PID: {process.pid})')
        
        # 等待服务启动
        print('等待服务启动...')
        time.sleep(3)
        
        # 验证启动是否成功
        running, pid, port_ok, log_ok = check_signal_receiver_running()
        
        if running and port_ok:
            print('✅ 信号接收服务启动验证成功')
            return True
        else:
            print('❌ 信号接收服务启动验证失败')
            return False
            
    except Exception as e:
        print(f'❌ 启动信号接收服务失败: {e}')
        return False

def stop_signal_receiver():
    """停止信号接收服务"""
    print('\n=== 停止信号接收服务 ===')
    
    stopped = False
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    pid = proc.info['pid']
                    print(f'正在停止信号接收服务 (PID: {pid})...')
                    
                    try:
                        psutil.Process(pid).terminate()
                        time.sleep(2)
                        
                        # 检查是否已停止
                        if not psutil.pid_exists(pid):
                            print(f'✅ 信号接收服务已停止')
                            stopped = True
                        else:
                            # 强制杀死
                            psutil.Process(pid).kill()
                            print(f'✅ 信号接收服务已强制停止')
                            stopped = True
                            
                    except Exception as e:
                        print(f'❌ 停止服务失败: {e}')
                    
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if not stopped:
        print('⚠️ 没有找到运行中的信号接收服务')
    
    return stopped

def check_signal_receiver_logs():
    """检查信号接收服务日志"""
    print('\n=== 检查信号接收服务日志 ===')
    
    log_files = [
        'logs/signal_receiver.log',
        'logs/signal_receiver.out.log', 
        'logs/signal_receiver.err.log'
    ]
    
    for log_file in log_files:
        print(f'\n--- {log_file} ---')
        
        if not os.path.exists(log_file):
            print(f'❌ 日志文件不存在: {log_file}')
            continue
        
        try:
            # 读取最后几行
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            if not lines:
                print(f'⚠️ 日志文件为空: {log_file}')
                continue
            
            # 显示最后5行
            print(f'最后5行内容:')
            for line in lines[-5:]:
                print(f'  {line.strip()}')
                
        except Exception as e:
            print(f'❌ 读取日志文件失败: {e}')

def test_signal_receiver():
    """测试信号接收服务"""
    print('\n=== 测试信号接收服务 ===')
    
    try:
        import requests
        
        # 获取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        server_port = config.get('server_port', 9999)
        
        # 发送测试请求
        test_url = f'http://localhost:{server_port}/health'
        
        print(f'发送测试请求到: {test_url}')
        
        response = requests.get(test_url, timeout=5)
        
        if response.status_code == 200:
            print('✅ 信号接收服务响应正常')
            print(f'响应内容: {response.text}')
            return True
        else:
            print(f'❌ 信号接收服务响应异常: {response.status_code}')
            return False
            
    except requests.exceptions.ConnectionError:
        print('❌ 无法连接到信号接收服务')
        return False
    except Exception as e:
        print(f'❌ 测试信号接收服务失败: {e}')
        return False

def main():
    """主函数"""
    print('信号接收服务检查和修复工具')
    print('=' * 50)
    
    # 1. 检查当前状态
    running, pid, port_ok, log_ok = check_signal_receiver_running()
    
    # 2. 如果服务未运行，尝试启动
    if not running:
        print('\n🔄 信号接收服务未运行，正在启动...')
        
        if start_signal_receiver():
            print('\n✅ 信号接收服务启动成功')
            
            # 测试服务
            if test_signal_receiver():
                print('✅ 信号接收服务测试通过')
            else:
                print('⚠️ 信号接收服务测试失败，但服务已启动')
        else:
            print('\n❌ 信号接收服务启动失败')
            
            # 检查日志以了解失败原因
            check_signal_receiver_logs()
            
            print('\n建议解决方案:')
            print('1. 检查端口是否被占用')
            print('2. 检查config.json配置文件')
            print('3. 检查Python环境和依赖')
            print('4. 查看错误日志了解具体原因')
    else:
        print('\n✅ 信号接收服务已在运行')
        
        # 测试服务
        if test_signal_receiver():
            print('✅ 信号接收服务测试通过')
        else:
            print('⚠️ 信号接收服务测试失败，可能需要重启')
            
            # 询问是否重启
            print('\n是否重启信号接收服务? (自动重启)')
            
            # 重启服务
            if stop_signal_receiver():
                time.sleep(2)
                if start_signal_receiver():
                    print('✅ 信号接收服务重启成功')
                else:
                    print('❌ 信号接收服务重启失败')
    
    # 3. 最终状态检查
    print('\n=== 最终状态检查 ===')
    running, pid, port_ok, log_ok = check_signal_receiver_running()
    
    if running and port_ok:
        print('🎉 信号接收服务运行正常！')
        
        # 显示服务信息
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            server_port = config.get('server_port', 9999)
            enable_trading = config.get('enable_trading', False)
            
            print(f'服务端口: {server_port}')
            print(f'交易开关: {"开启" if enable_trading else "关闭"}')
            print(f'进程ID: {pid}')
            
        except Exception as e:
            print(f'获取配置信息失败: {e}')
    else:
        print('❌ 信号接收服务仍有问题，请手动检查')

if __name__ == "__main__":
    main()
