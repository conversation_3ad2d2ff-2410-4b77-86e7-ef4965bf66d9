#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试平仓通知功能
"""

import json
import requests
import time

def test_web_close_notification():
    """测试Web界面平仓通知"""
    print("=== 测试Web界面平仓通知功能 ===")
    
    # 首先获取当前的活跃订单
    try:
        response = requests.get('http://localhost:8080/api/account_info', timeout=10)
        if response.status_code == 200:
            print("✅ Web服务器连接正常")
        else:
            print("❌ Web服务器连接失败")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Web服务器: {e}")
        return False
    
    # 获取活跃订单
    try:
        response = requests.get('http://localhost:8080/api/positions', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('positions'):
                positions = data['positions']
                print(f"找到 {len(positions)} 个活跃持仓")
                
                # 显示前几个持仓
                for i, pos in enumerate(positions[:3]):
                    print(f"  持仓{i+1}: 订单号{pos.get('ticket')}, 交易对{pos.get('symbol')}, 盈亏{pos.get('profit', 0):.2f}")
                
                return True
            else:
                print("当前没有活跃持仓可以测试")
                return False
        else:
            print("❌ 获取持仓信息失败")
            return False
    except Exception as e:
        print(f"❌ 获取持仓信息出错: {e}")
        return False

def test_bark_notification_directly():
    """直接测试Bark通知"""
    print("\n=== 直接测试Bark通知 ===")
    
    try:
        import bark_notifier
        
        # 测试平仓通知
        test_trade_info = {
            'ticket': 88888888,
            'symbol': 'XAUUSD',
            'operation': 'buy',
            'volume': 0.1,
            'open_price': 1950.00,
            'close_price': 1955.00,
            'profit': 50.00,
            'points': 500
        }
        
        print("发送测试平仓通知...")
        result = bark_notifier.notify_trade_closed(test_trade_info)
        
        if result:
            print("✅ 测试平仓通知发送成功")
            print("请检查您的Bark设备是否收到通知")
            return True
        else:
            print("❌ 测试平仓通知发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试通知失败: {e}")
        return False

def check_notification_config():
    """检查通知配置"""
    print("\n=== 检查通知配置 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        bark_notifications = config.get('bark_notifications', {})
        
        print("当前通知配置:")
        notification_types = {
            'signal_received': '📡 信号接收通知',
            'signal_processing': '⚡ 信号处理通知', 
            'trade_execution': '💰 交易执行通知',
            'trade_closed': '🔄 平仓通知',
            'error': '❌ 错误通知'
        }
        
        for key, name in notification_types.items():
            enabled = bark_notifications.get(key, True)
            status = "启用" if enabled else "禁用"
            emoji = "✅" if enabled else "❌"
            print(f"  {emoji} {name}: {status}")
        
        # 检查平仓通知是否启用
        trade_closed_enabled = bark_notifications.get('trade_closed', True)
        if trade_closed_enabled:
            print("\n✅ 平仓通知已启用")
            return True
        else:
            print("\n❌ 平仓通知被禁用！")
            return False
            
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False

def simulate_manual_close():
    """模拟手动平仓操作"""
    print("\n=== 模拟手动平仓操作 ===")
    
    try:
        import mt5_trader
        
        # 获取当前持仓
        positions = mt5_trader.get_all_positions()
        
        if not positions:
            print("当前没有持仓可以测试")
            return False
        
        print(f"找到 {len(positions)} 个持仓")
        
        # 选择一个持仓进行模拟（不实际平仓）
        test_position = positions[0]
        ticket = test_position['ticket']
        
        print(f"模拟平仓订单: {ticket}")
        print("注意: 这只是模拟，不会实际平仓")
        
        # 模拟平仓通知
        trade_info = {
            'ticket': ticket,
            'symbol': test_position['symbol'],
            'operation': 'buy' if test_position['type'] == 0 else 'sell',
            'volume': test_position['volume'],
            'open_price': test_position['price_open'],
            'close_price': test_position['price_current'],
            'profit': test_position['profit'],
            'points': int((test_position['price_current'] - test_position['price_open']) * 100000)
        }
        
        import bark_notifier
        result = bark_notifier.notify_trade_closed(trade_info)
        
        if result:
            print("✅ 模拟平仓通知发送成功")
            return True
        else:
            print("❌ 模拟平仓通知发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 模拟平仓失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试平仓通知功能...")
    print("=" * 60)
    
    tests = [
        ("通知配置检查", check_notification_config),
        ("直接Bark通知测试", test_bark_notification_directly),
        ("Web界面连接测试", test_web_close_notification),
        ("模拟平仓通知测试", simulate_manual_close),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n测试结果: {success_count}/{len(results)} 项成功")
    
    if success_count == len(results):
        print("\n🎉 所有测试通过！平仓通知功能应该正常工作。")
        print("\n如果您手动平仓仍然没有收到通知，请确认:")
        print("1. 是通过Web界面的平仓按钮操作，而不是直接在MT5客户端操作")
        print("2. 平仓操作确实成功执行")
        print("3. 网络连接正常，Bark服务器可达")
    else:
        print(f"\n⚠️ 有 {len(results) - success_count} 项测试失败，可能影响平仓通知功能。")

if __name__ == "__main__":
    main()
