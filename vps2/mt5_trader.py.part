def get_all_positions():
    """获取MT5所有活跃持仓"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法获取持仓")
        return []
    
    try:
        # 获取所有活跃持仓
        positions = mt5.positions_get()
        if positions is None or len(positions) == 0:
            logger.info("当前没有活跃持仓")
            return []
        
        positions_list = []
        for position in positions:
            position_info = {
                'ticket': position.ticket,
                'time': position.time,
                'time_msc': position.time_msc,
                'time_update': position.time_update,
                'type': position.type,  # 0表示买入，1表示卖出
                'type_str': 'buy' if position.type == 0 else 'sell',
                'magic': position.magic,
                'position_id': position.identifier,
                'reason': position.reason,
                'volume': position.volume,
                'price_open': position.price_open,
                'sl': position.sl,
                'tp': position.tp,
                'price_current': position.price_current,
                'swap': position.swap,
                'profit': position.profit,
                'symbol': position.symbol,
                'comment': position.comment,
                'external_id': position.external_id
            }
            positions_list.append(position_info)
        
        logger.info(f"获取到{len(positions_list)}个活跃持仓")
        return positions_list
    
    except Exception as e:
        logger.error(f"获取持仓时发生错误: {e}", exc_info=True)
        return []

def get_all_orders():
    """获取MT5所有挂单（未执行的订单）"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法获取挂单")
        return []
    
    try:
        # 获取所有挂单
        orders = mt5.orders_get()
        if orders is None or len(orders) == 0:
            logger.info("当前没有挂单")
            return []
        
        orders_list = []
        for order in orders:
            order_info = {
                'ticket': order.ticket,
                'time_setup': order.time_setup,
                'time_setup_msc': order.time_setup_msc,
                'time_expiration': order.time_expiration,
                'type': order.type,
                'type_str': get_order_type_string(order.type),
                'state': order.state,
                'magic': order.magic,
                'position_id': order.position_id,
                'reason': order.reason,
                'volume_initial': order.volume_initial,
                'volume_current': order.volume_current,
                'price_open': order.price_open,
                'sl': order.sl,
                'tp': order.tp,
                'price_current': order.price_current,
                'symbol': order.symbol,
                'comment': order.comment,
                'external_id': order.external_id
            }
            orders_list.append(order_info)
        
        logger.info(f"获取到{len(orders_list)}个挂单")
        return orders_list
    
    except Exception as e:
        logger.error(f"获取挂单时发生错误: {e}", exc_info=True)
        return []

def get_order_type_string(order_type):
    """获取订单类型的字符串描述"""
    order_types = {
        mt5.ORDER_TYPE_BUY: "买入市价单",
        mt5.ORDER_TYPE_SELL: "卖出市价单",
        mt5.ORDER_TYPE_BUY_LIMIT: "买入限价单",
        mt5.ORDER_TYPE_SELL_LIMIT: "卖出限价单",
        mt5.ORDER_TYPE_BUY_STOP: "买入停损单",
        mt5.ORDER_TYPE_SELL_STOP: "卖出停损单",
        mt5.ORDER_TYPE_BUY_STOP_LIMIT: "买入止损限价单",
        mt5.ORDER_TYPE_SELL_STOP_LIMIT: "卖出止损限价单"
    }
    
    return order_types.get(order_type, f"未知类型({order_type})")

def close_position(ticket):
    """平仓指定持仓"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法平仓")
        return {
            'success': False,
            'error': 'MT5未连接'
        }
    
    try:
        # 获取持仓信息
        position = mt5.positions_get(ticket=ticket)
        if position is None or len(position) == 0:
            logger.error(f"未找到持仓: {ticket}")
            return {
                'success': False,
                'error': f'未找到持仓: {ticket}'
            }
        
        position = position[0]
        
        # 准备反向交易请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "position": ticket,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": mt5.ORDER_TYPE_SELL if position.type == 0 else mt5.ORDER_TYPE_BUY,
            "price": mt5.symbol_info_tick(position.symbol).bid if position.type == 0 else mt5.symbol_info_tick(position.symbol).ask,
            "deviation": 20,  # 允许的价格偏差点数
            "magic": position.magic,
            "comment": f"平仓 #{ticket}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        # 尝试不同的成交模式
        result = try_order_with_filling_modes(request)
        
        if result['success']:
            logger.info(f"成功平仓: #{ticket}, 交易量: {position.volume}, 利润: {position.profit}")
            return {
                'success': True,
                'ticket': result.get('order', 0),
                'volume': position.volume,
                'profit': position.profit
            }
        else:
            logger.error(f"平仓失败: #{ticket}, 错误: {result.get('error', '未知错误')}")
            return {
                'success': False,
                'error': result.get('error', '未知错误')
            }
    
    except Exception as e:
        logger.error(f"平仓时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'error': str(e)
        }

def cancel_order(ticket):
    """取消挂单"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法取消订单")
        return {
            'success': False,
            'error': 'MT5未连接'
        }
    
    try:
        # 准备删除订单请求
        request = {
            "action": mt5.TRADE_ACTION_REMOVE,
            "order": ticket,
        }
        
        # 发送请求
        result = mt5.order_send(request)
        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
            logger.info(f"成功取消订单: #{ticket}")
            return {
                'success': True
            }
        else:
            error = mt5.last_error()
            logger.error(f"取消订单失败: #{ticket}, 错误: {error}")
            return {
                'success': False,
                'error': str(error)
            }
    
    except Exception as e:
        logger.error(f"取消订单时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'error': str(e)
        }
