#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易信号接收服务器 - 接收来自VPS1的交易信号
"""

import json
import logging
import os
import sqlite3
import threading
from datetime import datetime
from flask import Flask, request, jsonify
import time
import sys

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    filename='logs/signal_receiver.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库操作的线程锁
DB_LOCK = threading.Lock()

# 载入配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    SERVER_PORT = config.get('server_port', 9999)
    ENABLE_TRADING = config.get('enable_trading', False)
    
    # 配置日志级别
    log_level = config.get('log_level', 'DEBUG')
    numeric_level = getattr(logging, log_level.upper(), None)
    if isinstance(numeric_level, int):
        logging.getLogger().setLevel(numeric_level)
    
    logger.info(f"配置加载成功，服务端口: {SERVER_PORT}，交易开关: {'开启' if ENABLE_TRADING else '关闭'}")
except Exception as e:
    logger.error(f"加载配置文件失败: {e}", exc_info=True)
    SERVER_PORT = 9999
    ENABLE_TRADING = False

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        c.execute("SELECT 1")
        conn.close()

        # 检查交易开关状态
        trading_enabled = is_trading_enabled()

        return jsonify({
            'status': 'healthy',
            'service': 'signal_receiver',
            'port': SERVER_PORT,
            'trading_enabled': trading_enabled,
            'timestamp': datetime.now().isoformat()
        }), 200
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def init_database():
    """初始化数据库，创建必要的表"""
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        # 创建信号存储表
        c.execute('''CREATE TABLE IF NOT EXISTS signals
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      timestamp TEXT,
                      trading_pair TEXT,
                      signal_type TEXT,
                      interval TEXT,
                      mrc_event TEXT,
                      mrc_timestamp TEXT,
                      mean REAL,
                      r1 REAL,
                      s1 REAL,
                      r2 REAL,
                      s2 REAL,
                      r2_9 REAL,
                      s2_9 REAL,
                      r2_1 REAL,
                      s2_1 REAL,
                      original_timeframe TEXT,
                      close_price REAL,
                      tmfs_signal TEXT,
                      tmfs_price REAL,
                      rsi_signal TEXT,
                      rsi_value REAL,
                      processed INTEGER DEFAULT 0,
                      process_time TEXT,
                      order_ticket INTEGER,
                      order_result TEXT,
                      webhook_content TEXT,
                      process_status TEXT DEFAULT 'pending',
                      failure_reason TEXT,
                      success_details TEXT)''')

        # 创建订单表
        c.execute('''CREATE TABLE IF NOT EXISTS orders
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      timestamp TEXT,
                      ticket INTEGER,
                      trading_pair TEXT,
                      operation TEXT,
                      volume REAL,
                      price REAL,
                      sl REAL,
                      tp REAL,
                      signal_id INTEGER,
                      status TEXT,
                      profit REAL DEFAULT 0,
                      close_price REAL,
                      close_time TEXT,
                      mean REAL,
                      r1 REAL,
                      r2 REAL,
                      s1 REAL,
                      s2 REAL,
                      FOREIGN KEY(signal_id) REFERENCES signals(id))''')
        
        # 检查现有订单表中是否有关联价格级别的列，如果没有则添加这些列
        try:
            # 先检查一个列是否存在
            c.execute("SELECT mean FROM orders LIMIT 1")
        except sqlite3.OperationalError:
            logger.info("添加价格级别列到订单表")
            try:
                # 添加缺失的列
                for column_name in ['mean', 'r1', 'r2', 's1', 's2']:
                    try:
                        c.execute(f"ALTER TABLE orders ADD COLUMN {column_name} REAL")
                        logger.info(f"添加列 {column_name} 到订单表")
                    except sqlite3.OperationalError:
                        # 列已存在，跳过
                        pass
            except Exception as e:
                logger.error(f"更新订单表结构时出错: {e}", exc_info=True)

        # 检查并添加新的MRC扩展字段到signals表
        new_signal_columns = ['r2_9', 's2_9', 'r2_1', 's2_1', 'original_timeframe']
        for column_name in new_signal_columns:
            try:
                # 尝试查询该列，如果不存在会抛出异常
                c.execute(f"SELECT {column_name} FROM signals LIMIT 1")
            except sqlite3.OperationalError:
                # 列不存在，添加它
                try:
                    if column_name == 'original_timeframe':
                        c.execute(f"ALTER TABLE signals ADD COLUMN {column_name} TEXT")
                    else:
                        c.execute(f"ALTER TABLE signals ADD COLUMN {column_name} REAL")
                    logger.info(f"添加列 {column_name} 到signals表")
                except sqlite3.OperationalError as e:
                    logger.warning(f"添加列 {column_name} 失败: {e}")
                except Exception as e:
                    logger.error(f"添加列 {column_name} 时出错: {e}", exc_info=True)
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化成功")
    except sqlite3.Error as e:
        logger.error(f"数据库初始化失败: {e}", exc_info=True)
        sys.exit(1)

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}", exc_info=True)
        raise

def get_symbol_digits(symbol):
    """获取交易品种的小数位数"""
    # 根据不同交易品种返回合适的小数位数
    if "XAUUSD" in symbol:  # 黄金
        return 2
    elif "BTCUSD" in symbol:  # 比特币
        return 1
    elif "ETHUSD" in symbol:  # 以太坊
        return 1
    elif "JPY" in symbol:  # 日元对
        return 3
    else:
        return 5  # 其他品种默认值

def round_price(value, symbol):
    """根据交易品种舍入价格到适当的小数位"""
    if value is None:
        return None
    try:
        digits = get_symbol_digits(symbol)
        return round(float(value), digits)
    except (ValueError, TypeError) as e:
        logger.error(f"价格舍入失败: {e}")
        return value

def store_signal(signal_data):
    """存储接收到的信号"""
    with DB_LOCK:
        try:
            conn = get_db_connection()
            c = conn.cursor()
            
            # 获取交易对
            trading_pair = signal_data.get('trading_pair')
            
            # 根据交易对处理小数位
            mean = round_price(signal_data.get('mean'), trading_pair)
            r1 = round_price(signal_data.get('r1'), trading_pair)
            s1 = round_price(signal_data.get('s1'), trading_pair)
            r2 = round_price(signal_data.get('r2'), trading_pair)
            s2 = round_price(signal_data.get('s2'), trading_pair)
            r2_9 = round_price(signal_data.get('r2_9'), trading_pair)
            s2_9 = round_price(signal_data.get('s2_9'), trading_pair)
            r2_1 = round_price(signal_data.get('r2_1'), trading_pair)
            s2_1 = round_price(signal_data.get('s2_1'), trading_pair)
            close_price = round_price(signal_data.get('close_price'), trading_pair)
            tmfs_price = round_price(signal_data.get('tmfs_price'), trading_pair)
            original_timeframe = signal_data.get('original_timeframe')
            
            # 插入信号数据
            c.execute('''INSERT INTO signals
                         (timestamp, trading_pair, signal_type, interval, mrc_event,
                          mrc_timestamp, mean, r1, s1, r2, s2, r2_9, s2_9, r2_1, s2_1,
                          original_timeframe, close_price, tmfs_signal, tmfs_price,
                          rsi_signal, rsi_value, webhook_content, process_status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                      (signal_data.get('timestamp'),
                       trading_pair,
                       signal_data.get('signal_type'),
                       signal_data.get('interval'),
                       signal_data.get('mrc_event'),
                       signal_data.get('mrc_timestamp'),
                       mean,
                       r1,
                       s1,
                       r2,
                       s2,
                       r2_9,
                       s2_9,
                       r2_1,
                       s2_1,
                       original_timeframe,
                       close_price,
                       signal_data.get('tmfs_signal'),
                       tmfs_price,
                       signal_data.get('rsi_signal'),
                       signal_data.get('rsi_value'),
                       json.dumps(signal_data, ensure_ascii=False, indent=2),  # 存储完整webhook内容
                       'pending'))
            
            signal_id = c.lastrowid
            conn.commit()
            conn.close()

            logger.info(f"存储信号成功，ID: {signal_id}, 交易对: {signal_data.get('trading_pair')}, 信号类型: {signal_data.get('signal_type')}")

            # 发送信号接收通知
            try:
                import bark_notifier
                signal_info_for_notification = signal_data.copy()
                signal_info_for_notification['signal_id'] = signal_id
                bark_notifier.notify_signal_received(signal_info_for_notification)
            except Exception as e:
                logger.error(f"发送信号接收通知失败: {e}")

            return signal_id
        except sqlite3.Error as e:
            logger.error(f"存储信号失败: {e}", exc_info=True)
            return None

def notify_mt5_trader(signal_id):
    """发送信号到MT5交易模块"""
    try:
        # 通过创建一个信号文件来通知MT5交易模块
        # 这避免了进程间通信的复杂性
        with open('signal_pending.txt', 'w') as f:
            f.write(str(signal_id))
        logger.info(f"已通知MT5交易模块处理信号ID: {signal_id}")
        return True
    except Exception as e:
        logger.error(f"通知MT5交易模块失败: {e}", exc_info=True)
        return False

@app.route('/trade_signal', methods=['POST'])
def trade_signal():
    try:
        signal_data = request.get_json()
        if not signal_data:
            logger.warning("收到空的信号数据")
            return jsonify({'status': 'error', 'message': '信号数据为空'}), 400
        
        # 检查必要的交易信息是否存在
        if 'trading_pair' not in signal_data or 'signal_type' not in signal_data:
            logger.warning("信号缺少必要的交易对或信号类型")
            return jsonify({'status': 'error', 'message': '信号缺少必要的交易对或信号类型'}), 400
            
        # 预处理信号价格数据，确保数值类型是浮点数
        try:
            # 尝试转换价格相关字段为浮点数
            price_fields = ['mean', 'r1', 's1', 'r2', 's2', 'r2_9', 's2_9', 'r2_1', 's2_1',
                           'close_price', 'tmfs_price', 'rsi_value']
            for field in price_fields:
                if field in signal_data and signal_data[field] is not None:
                    signal_data[field] = float(signal_data[field])
        except ValueError as e:
            logger.warning(f"信号数据格式错误: {e}")
            # 不返回错误，尝试继续处理
            
        logger.info(f"收到交易信号: {json.dumps(signal_data, ensure_ascii=False)}")
        
        # 存储信号 (已包含舍入处理)
        signal_id = store_signal(signal_data)
        
        if signal_id is None:
            return jsonify({'status': 'error', 'message': '存储信号失败'}), 500
        
        # 检查交易开关
        if not is_trading_enabled():
            logger.warning("交易功能关闭，信号已存储但不会执行交易")
            return jsonify({'status': 'success', 'message': '信号已存储，但交易功能已关闭'}), 200
        
        # 通知MT5交易模块
        if notify_mt5_trader(signal_id):
            return jsonify({'status': 'success', 'message': '信号已接收并通知MT5交易模块'}), 200
        else:
            return jsonify({'status': 'error', 'message': '通知MT5交易模块失败'}), 500
    
    except Exception as e:
        logger.error(f"处理交易信号时发生错误: {e}", exc_info=True)
        return jsonify({'status': 'error', 'message': f'服务器错误: {str(e)}'}), 500

def is_trading_enabled():
    """检查交易开关状态"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('enable_trading', False)
    except Exception as e:
        logger.error(f"检查交易开关失败: {e}", exc_info=True)
        return False

if __name__ == '__main__':
    try:
        init_database()
        logger.info(f"交易信号接收服务器启动, 端口: {SERVER_PORT}")
        app.run(host='0.0.0.0', port=SERVER_PORT)
    except Exception as e:
        logger.error(f"服务器启动失败: {e}", exc_info=True)
        sys.exit(1)
