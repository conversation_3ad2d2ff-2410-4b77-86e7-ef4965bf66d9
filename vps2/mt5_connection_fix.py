#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5连接问题诊断和解决方案
"""
import os
import sys
import json
import datetime
import shutil
import platform

def print_header(text):
    print("\n" + "=" * 50)
    print(text)
    print("=" * 50)

def print_section(text):
    print("\n" + "-" * 40)
    print(text)
    print("-" * 40)

# 收集系统信息
print_header("系统信息")
print(f"操作系统: {platform.platform()}")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

# 检查配置文件
print_header("检查配置文件")
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    mt5_path = config.get('mt5_path', '')
    mt5_login = config.get('mt5_login', '')
    mt5_server = config.get('mt5_server', '')
    
    print(f"MT5路径: {mt5_path}")
    print(f"MT5登录ID: {mt5_login}")
    print(f"MT5服务器: {mt5_server}")
    
    # 检查文件是否存在
    if os.path.exists(mt5_path):
        print(f"✓ MT5路径存在")
        if os.path.isfile(mt5_path):
            print(f"✓ 是有效文件")
        else:
            print(f"✗ 不是文件")
    else:
        print(f"✗ MT5路径不存在")
        
except Exception as e:
    print(f"✗ 读取配置文件出错: {e}")

# 尝试导入MetaTrader5模块
print_header("导入MetaTrader5模块")
try:
    import MetaTrader5 as mt5
    print(f"✓ MetaTrader5模块导入成功")
    print(f"MetaTrader5模块路径: {mt5.__file__}")
    
    # 尝试初始化
    print_section("尝试初始化MT5")
    # 关闭已有连接
    mt5.shutdown()
    print("已关闭之前的连接")
    
    # 不带路径初始化
    print("尝试不带路径初始化...")
    init_result = mt5.initialize()
    print(f"结果: {'✓ 成功' if init_result else '✗ 失败'}")
    if not init_result:
        print(f"错误: {mt5.last_error()}")
    
    # 使用配置文件路径初始化
    if not init_result:
        print("\n尝试使用配置文件路径初始化...")
        init_result = mt5.initialize(path=mt5_path)
        print(f"结果: {'✓ 成功' if init_result else '✗ 失败'}")
        if not init_result:
            print(f"错误: {mt5.last_error()}")
            
    # 尝试使用常见路径
    if not init_result:
        common_paths = [
            "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
            "C:\\Program Files (x86)\\MetaTrader 5\\terminal.exe"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                print(f"\n尝试使用路径 {path} 初始化...")
                init_result = mt5.initialize(path=path)
                print(f"结果: {'✓ 成功' if init_result else '✗ 失败'}")
                if init_result:
                    print(f"✓ 使用路径 {path} 初始化成功")
                    break
                else:
                    print(f"✗ 错误: {mt5.last_error()}")
    
    # 如果初始化成功，检查终端信息
    if init_result:
        print_section("MT5终端信息")
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"✓ 终端信息获取成功")
            print(f"终端名称: {terminal_info.name}")
            print(f"终端版本: {terminal_info.version}")
            print(f"终端路径: {terminal_info.path}")
            print(f"数据路径: {terminal_info.data_path}")
            print(f"交易允许: {terminal_info.trade_allowed}")
            
            # 尝试登录
            print_section("尝试登录MT5账户")
            try:
                login = int(mt5_login)
                password = config.get('mt5_password', '')
                server = mt5_server
                
                print(f"登录信息: 账号={login}, 服务器={server}")
                login_result = mt5.login(login=login, password=password, server=server)
                print(f"登录结果: {'✓ 成功' if login_result else '✗ 失败'}")
                
                if not login_result:
                    print(f"错误: {mt5.last_error()}")
                    
                    # 尝试不指定服务器登录
                    print("\n尝试不指定服务器登录...")
                    login_result = mt5.login(login=login, password=password)
                    print(f"结果: {'✓ 成功' if login_result else '✗ 失败'}")
                    
                    if not login_result:
                        print(f"错误: {mt5.last_error()}")
                
                # 如果登录成功，获取账户信息
                if login_result:
                    print_section("账户信息")
                    account_info = mt5.account_info()
                    if account_info:
                        print(f"✓ 账户信息获取成功")
                        print(f"账户名称: {account_info.name}")
                        print(f"账户号码: {account_info.login}")
                        print(f"服务器名称: {account_info.server}")
                        print(f"账户货币: {account_info.currency}")
                        print(f"账户余额: {account_info.balance}")
                        print(f"账户净值: {account_info.equity}")
                        print(f"浮动盈亏: {account_info.profit}")
                    else:
                        print(f"✗ 账户信息获取失败")
                        print(f"错误: {mt5.last_error()}")
            except Exception as e:
                print(f"✗ 登录过程出错: {e}")
        else:
            print(f"✗ 无法获取终端信息")
            print(f"错误: {mt5.last_error()}")
    
    # 关闭连接
    mt5.shutdown()
    print("\nMT5连接已关闭")
    
except ImportError:
    print(f"✗ 无法导入MetaTrader5模块")
except Exception as e:
    print(f"✗ MT5测试过程中出错: {e}")

# 提供解决方案
print_header("解决方案")
print("""
1. 确保MetaTrader 5在本地正常运行并且已经登录到您的账户
2. 确保配置文件中的MT5路径正确指向terminal64.exe
3. 确认登录ID、密码和服务器信息正确无误
4. 请尝试以下修改配置文件的方法:
   - 将MT5路径更新为准确路径 (例如: C:\\Program Files\\MetaTrader 5\\terminal64.exe)
   - 确保MT5登录ID是纯数字
   - 尝试启动MetaTrader 5并在程序中手动登录，然后再启动交易系统
   
5. 如果问题仍然存在，检查以下可能的解决方案:
   - 重新安装MetaTrader 5
   - 确保您已启用自动交易功能 (点击MT5界面顶部的"自动交易"按钮)
   - 检查系统防火墙和安全软件是否阻止MT5进程的通信
""")

print_header("建议配置修改")
print("""
请更新config.json文件，使用以下配置:

{
    "mt5_path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
    "mt5_login": "25197838",
    "mt5_password": "i2Y_[T/SV8zl",
    "mt5_server": "Tickmill-Demo",
    ...其他配置保持不变...
}

注意：服务器名称改为"Tickmill-Demo"而不是"demo.mt5tickmill.com"
""")

print("\n完成时间:", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
