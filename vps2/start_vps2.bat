@echo off
REM VPS2交易系统启动脚本
REM 自动切换到项目目录并启动主程序

echo =======================================
echo        VPS2 MT5交易系统启动器
echo =======================================
echo.

REM 获取脚本所在目录
cd /d "%~dp0"

REM 显示当前目录
echo 当前工作目录: %CD%
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已正确安装并添加到PATH
    echo.
    pause
    exit /b 1
)

REM 显示Python版本
echo Python版本信息:
python --version
echo.

REM 检查main.py是否存在
if not exist "main.py" (
    echo 错误: 未找到main.py文件
    echo 请确保脚本在正确的项目目录中运行
    echo.
    pause
    exit /b 1
)

REM 创建日志目录（如果不存在）
if not exist "logs" (
    echo 创建日志目录...
    mkdir logs
)

REM 检查配置文件
if not exist "config.json" (
    echo 警告: 未找到config.json配置文件
    if exist "config_fixed.json" (
        echo 发现config_fixed.json，将其复制为config.json
        copy "config_fixed.json" "config.json"
    )
)

REM 检查登录安全功能依赖
echo Checking login security dependencies...
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo Warning: PIL/Pillow not found, captcha may not work
    echo Suggest running: pip install Pillow
    echo.
) else (
    echo [OK] PIL/Pillow installed, captcha available
)

REM 初始化登录安全数据库表
echo Initializing login security database tables...
python -c "from web_interface import init_login_security_tables; init_login_security_tables()" >nul 2>&1
if errorlevel 1 (
    echo Warning: Login security database initialization failed
) else (
    echo [OK] Login security database initialized
)
echo.

echo Starting VPS2 Trading System...
echo.
echo Security Features:
echo - Login Captcha: Auto-enabled after 3 failed attempts in 1 minute
echo - Login Tracking: All login attempts are recorded
echo - Session Security: 24-hour session lifetime
echo.
echo Press Ctrl+C to stop the system
echo.

REM 启动主程序
python main.py

REM 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查日志文件获取详细信息
    echo.
)

echo.
echo 系统已关闭
pause
