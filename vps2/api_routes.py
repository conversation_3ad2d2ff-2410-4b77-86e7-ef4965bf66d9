#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API路由模块 - VPS2的API接口
"""

import logging
import json
from flask import Blueprint, jsonify, request, session
from functools import wraps
import mt5_trader
import MetaTrader5 as mt5

# 配置日志
logger = logging.getLogger(__name__)

# 创建Blueprint
api = Blueprint('api', __name__)

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '未登录，请先登录'})
        return f(*args, **kwargs)
    return decorated_function

# 路由: 应用止盈止损设置到所有活跃订单
@api.route('/apply_sl_tp_to_all', methods=['POST'])
@login_required
def apply_sl_tp_to_all():
    """将当前配置中的止盈止损点数应用到所有活跃订单"""
    try:
        # 确保MT5已初始化
        if not mt5_trader.mt5_initialized and not mt5_trader.init_mt5():
            return jsonify({
                'success': False,
                'message': 'MetaTrader 5未连接'
            })
        
        # 调用MT5交易模块的函数应用止盈止损
        result = mt5_trader.apply_sl_tp_to_all_positions()
        
        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'updated_count': result['updated_count'],
            'failed_count': result['failed_count'],
            'details': result['details']
        })
        
    except Exception as e:
        logger.error(f"应用止盈止损设置时发生错误: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'应用止盈止损设置时发生错误: {str(e)}'
        })

# 路由: 获取北京时间
@api.route('/beijing_time')
@login_required
def beijing_time():
    """提供北京时间的API端点，用于前端更新显示"""
    try:
        from utils.beijing_time import get_beijing_time
        beijing_time = get_beijing_time()
        return jsonify({
            'beijing_time': beijing_time.strftime('%Y-%m-%d %H:%M:%S'),
            'success': True
        })
    except Exception as e:
        logger.error(f"获取北京时间时出错: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: 更新交易品种启用状态
@api.route('/update_symbol_status', methods=['POST'])
@login_required
def update_symbol_status():
    """更新特定交易品种的启用/禁用状态"""
    try:
        # 获取请求数据
        data = request.json
        if not data or 'symbol' not in data or 'enabled' not in data:
            return jsonify({
                'success': False,
                'message': '请求参数不完整，需要提供symbol和enabled字段'
            })
        
        symbol = data['symbol']
        enabled = data['enabled']
        
        # 加载配置
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return jsonify({
                'success': False,
                'message': f'加载配置文件失败: {str(e)}'
            })
        
        # 确保enabled_symbols字典存在
        if 'enabled_symbols' not in config:
            config['enabled_symbols'] = {}
        
        # 更新交易品种状态
        config['enabled_symbols'][symbol] = enabled
        
        # 保存配置
        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return jsonify({
                'success': False,
                'message': f'保存配置文件失败: {str(e)}'
            })
        
        # 重新加载MT5交易模块的配置
        mt5_trader.load_config()
        
        return jsonify({
            'success': True,
            'message': f"交易品种 {symbol} 已{'启用' if enabled else '禁用'}",
            'symbol': symbol,
            'enabled': enabled
        })
        
    except Exception as e:
        logger.error(f"更新交易品种状态时出错: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'更新交易品种状态时出错: {str(e)}'
        })

# 路由: 获取所有交易品种状态
@api.route('/get_symbols_status', methods=['GET'])
@login_required
def get_symbols_status():
    """获取所有交易品种的启用/禁用状态"""
    try:
        # 加载配置
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return jsonify({
                'success': False,
                'message': f'加载配置文件失败: {str(e)}'
            })
        
        # 获取交易品种状态
        enabled_symbols = config.get('enabled_symbols', {})
        
        # 为所有支持的交易品种提供默认状态
        all_symbols = ['XAUUSD', 'BTCUSD', 'ETHUSD', 'GBPUSD', 'GBPJPY', 'BRENT', 'XTIUSD', 'DXY']
        symbols_status = {}
        
        for symbol in all_symbols:
            symbols_status[symbol] = enabled_symbols.get(symbol, True)  # 默认为启用
        
        return jsonify({
            'success': True,
            'symbols_status': symbols_status
        })
        
    except Exception as e:
        logger.error(f"获取交易品种状态时出错: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取交易品种状态时出错: {str(e)}'
        })
