#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
升级VPS2数据库，添加新的MRC字段
"""
import sqlite3
import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def upgrade_database():
    """升级数据库，添加新字段"""
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        
        logger.info("开始升级数据库...")
        
        # 检查并添加新的MRC扩展字段到signals表
        new_signal_columns = ['r2_9', 's2_9', 'r2_1', 's2_1', 'original_timeframe']
        
        for column_name in new_signal_columns:
            try:
                # 尝试查询该列，如果不存在会抛出异常
                c.execute(f"SELECT {column_name} FROM signals LIMIT 1")
                logger.info(f"列 {column_name} 已存在")
            except sqlite3.OperationalError:
                # 列不存在，添加它
                try:
                    if column_name == 'original_timeframe':
                        c.execute(f"ALTER TABLE signals ADD COLUMN {column_name} TEXT")
                    else:
                        c.execute(f"ALTER TABLE signals ADD COLUMN {column_name} REAL")
                    logger.info(f"✅ 成功添加列 {column_name} 到signals表")
                except sqlite3.OperationalError as e:
                    logger.warning(f"❌ 添加列 {column_name} 失败: {e}")
                except Exception as e:
                    logger.error(f"❌ 添加列 {column_name} 时出错: {e}")
        
        # 提交更改
        conn.commit()
        
        # 验证表结构
        logger.info("\n验证更新后的表结构:")
        c.execute("PRAGMA table_info(signals)")
        columns = c.fetchall()
        
        found_new_fields = []
        for column in columns:
            column_name = column[1]
            column_type = column[2]
            logger.info(f"  {column_name:20} : {column_type}")
            
            if column_name in new_signal_columns:
                found_new_fields.append(column_name)
        
        logger.info(f"\n新字段检查结果:")
        for field in new_signal_columns:
            if field in found_new_fields:
                logger.info(f"  ✅ {field}")
            else:
                logger.info(f"  ❌ {field} (缺失)")
        
        conn.close()
        
        if len(found_new_fields) == len(new_signal_columns):
            logger.info("\n🎉 所有新字段都已成功添加到数据库!")
            return True
        else:
            logger.warning(f"\n⚠️  只添加了 {len(found_new_fields)}/{len(new_signal_columns)} 个新字段")
            return False
            
    except Exception as e:
        logger.error(f"❌ 升级数据库时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查数据库文件是否存在
    if not os.path.exists('trading_data.db'):
        logger.error("❌ 数据库文件 trading_data.db 不存在")
        logger.info("请先运行 signal_receiver.py 来创建数据库")
        sys.exit(1)
    
    # 升级数据库
    success = upgrade_database()
    
    if success:
        logger.info("✅ 数据库升级完成!")
    else:
        logger.error("❌ 数据库升级失败!")
        sys.exit(1)
