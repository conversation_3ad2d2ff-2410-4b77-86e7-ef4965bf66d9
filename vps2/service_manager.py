#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
服务状态监控和管理工具
"""

import os
import sys
import time
import json
import logging
import subprocess
import argparse
import psutil
import signal
import socket
from urllib.request import urlopen

# 配置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    filename='logs/service_manager.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def check_mt5_running():
    """检查MT5是否运行"""
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] in ['terminal64.exe', 'terminal.exe']:
            return True, proc.info['pid']
    return False, None

def check_signal_receiver_running():
    """检查信号接收服务是否运行"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    return True, proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False, None

def start_signal_receiver():
    """启动信号接收服务"""
    logger.info("正在启动信号接收服务...")
    try:
        # 使用Popen启动服务
        process = subprocess.Popen(
            ['python', 'signal_receiver.py'],
            stdout=open('logs/signal_receiver.out.log', 'a'),
            stderr=open('logs/signal_receiver.err.log', 'a'),
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        logger.info(f"信号接收服务启动成功，PID: {process.pid}")
        return True
    except Exception as e:
        logger.error(f"启动信号接收服务失败: {e}")
        return False

def restart_mt5_connection():
    """重启MT5连接"""
    logger.info("正在重置MT5连接...")
    try:
        result = subprocess.run(['python', 'restart_mt5_connection.py'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("MT5连接重置成功")
            return True
        else:
            logger.error(f"MT5连接重置失败: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"执行MT5连接重置脚本失败: {e}")
        return False

def check_web_interface_running():
    """检查Web界面是否运行"""
    # 从配置文件获取Web端口
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        web_port = config.get('web_port', 8080)
    except:
        web_port = 8080
    
    # 方法1: 检查端口是否被占用
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', web_port))
        sock.close()
        if result == 0:  # 端口已被占用
            # 方法2: 进一步验证是否是我们的Web服务
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'web_interface.py' in cmdline:
                            return True, proc.info['pid'], web_port
                except:
                    pass
            
            # 方法3: 尝试访问Web页面
            try:
                response = urlopen(f"http://127.0.0.1:{web_port}", timeout=2)
                if response.status == 200:
                    return True, None, web_port  # Web服务运行中，但无法确定PID
            except:
                pass
    except:
        pass
    
    return False, None, web_port

def start_web_interface():
    """启动Web界面服务"""
    logger.info("正在启动Web界面服务...")
    try:
        # 使用Popen启动服务
        process = subprocess.Popen(
            ['python', 'web_interface.py'],
            stdout=open('logs/web_interface.out.log', 'a'),
            stderr=open('logs/web_interface.err.log', 'a'),
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        logger.info(f"Web界面服务启动成功，PID: {process.pid}")
        return True
    except Exception as e:
        logger.error(f"启动Web界面服务失败: {e}")
        return False

def check_and_fix_services():
    """检查并修复服务状态"""
    # 检查MT5连接
    mt5_running, mt5_pid = check_mt5_running()
    if not mt5_running:
        logger.warning("MT5未运行，请手动启动MetaTrader 5软件")
        print("⚠️ MT5未运行，请手动启动MetaTrader 5软件")
    else:
        # 尝试重置MT5连接
        print(f"🔄 MT5已运行 (PID: {mt5_pid})，正在重置连接...")
        if restart_mt5_connection():
            print("✅ MT5连接已重置")
        else:
            print("❌ MT5连接重置失败，请查看日志")
    
    # 检查信号接收服务
    signal_running, signal_pid = check_signal_receiver_running()
    if not signal_running:
        print("🔄 信号接收服务未运行，正在启动...")
        if start_signal_receiver():
            print("✅ 信号接收服务已启动")
        else:
            print("❌ 信号接收服务启动失败，请查看日志")
    else:
        print(f"✅ 信号接收服务已运行 (PID: {signal_pid})")
      # 检查Web界面服务
    web_running, web_pid, web_port = check_web_interface_running()
    if not web_running:
        print(f"🔄 Web界面服务未运行，正在启动(端口: {web_port})...")
        if start_web_interface():
            print("✅ Web界面服务已启动")
        else:
            print("❌ Web界面服务启动失败，请查看日志")
    else:
        if web_pid:
            print(f"✅ Web界面服务已运行 (PID: {web_pid}, 端口: {web_port})")
        else:
            print(f"✅ Web界面服务已运行 (端口: {web_port})")
    
    # 读取配置文件，检查交易状态
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        trading_enabled = config.get('enable_trading', False)
        if trading_enabled:
            print("✅ 交易功能已启用")
        else:
            print("⚠️ 交易功能已禁用，可在系统设置中启用")
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        logger.error(f"读取配置文件失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT5交易系统服务管理工具')
    parser.add_argument('--fix', action='store_true', help='自动修复所有服务状态')
    parser.add_argument('--restart-web', action='store_true', help='重启Web界面服务')
    parser.add_argument('--restart-mt5', action='store_true', help='重置MT5连接')
    parser.add_argument('--restart-signal', action='store_true', help='重启信号接收服务')
    parser.add_argument('--status', action='store_true', help='查看所有服务状态')
    args = parser.parse_args()
    
    if args.fix:
        check_and_fix_services()
    elif args.restart_web:
        web_running, web_pid, web_port = check_web_interface_running()
        if web_running and web_pid:
            print(f"正在停止Web界面服务 (PID: {web_pid})...")
            try:
                psutil.Process(web_pid).terminate()
                time.sleep(2)  # 等待进程终止
            except Exception as e:
                logger.error(f"停止Web界面服务失败: {e}")
        
        print("正在启动Web界面服务...")
        if start_web_interface():
            print("✅ Web界面服务已重启")
        else:
            print("❌ Web界面服务重启失败")
    elif args.restart_mt5:
        if restart_mt5_connection():
            print("✅ MT5连接已重置")
        else:
            print("❌ MT5连接重置失败")
    elif args.restart_signal:
        signal_running, signal_pid = check_signal_receiver_running()
        if signal_running:
            print(f"正在停止信号接收服务 (PID: {signal_pid})...")
            try:
                psutil.Process(signal_pid).terminate()
                time.sleep(2)  # 等待进程终止
            except Exception as e:
                logger.error(f"停止信号接收服务失败: {e}")
        
        print("正在启动信号接收服务...")
        if start_signal_receiver():
            print("✅ 信号接收服务已重启")
        else:
            print("❌ 信号接收服务重启失败")
    elif args.status:
        # 检查MT5状态
        mt5_running, mt5_pid = check_mt5_running()
        if mt5_running:
            print(f"✅ MT5已运行 (PID: {mt5_pid})")
        else:
            print("❌ MT5未运行")
        
        # 检查信号接收服务
        signal_running, signal_pid = check_signal_receiver_running()
        if signal_running:
            print(f"✅ 信号接收服务已运行 (PID: {signal_pid})")
        else:
            print("❌ 信号接收服务未运行")
        
        # 检查Web界面服务
        web_running, web_pid, web_port = check_web_interface_running()
        if web_running:
            if web_pid:
                print(f"✅ Web界面服务已运行 (PID: {web_pid}, 端口: {web_port})")
            else:
                print(f"✅ Web界面服务已运行 (端口: {web_port})")
        else:
            print("❌ Web界面服务未运行")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
