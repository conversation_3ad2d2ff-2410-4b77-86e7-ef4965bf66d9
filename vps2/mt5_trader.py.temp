def get_server_time():
    """获取MT5服务器时间"""
    try:
        # 检查MT5是否已初始化
        if not mt5_initialized:
            if not init_mt5():
                logger.error("无法获取服务器时间：MT5未初始化")
                return datetime.now()  # 失败时返回本地时间
        
        # 通过EURUSD报价获取服务器时间（EURUSD是常见的交易对，一般都可用）
        tick = mt5.symbol_info_tick("EURUSD")
        if tick is not None and hasattr(tick, 'time'):
            server_time = datetime.fromtimestamp(tick.time)
            logger.debug(f"获取MT5服务器时间成功: {server_time}")
            return server_time
        else:
            # 尝试其他常见交易对
            for symbol in ["XAUUSD", "GBPUSD", "USDJPY"]:
                tick = mt5.symbol_info_tick(symbol)
                if tick is not None and hasattr(tick, 'time'):
                    server_time = datetime.fromtimestamp(tick.time)
                    logger.debug(f"通过{symbol}获取MT5服务器时间成功: {server_time}")
                    return server_time
            
            logger.error("无法获取MT5服务器时间：所有尝试的交易对都失败")
            return datetime.now()  # 失败时返回本地时间
    except Exception as e:
        logger.error(f"获取MT5服务器时间时出错: {e}", exc_info=True)
        return datetime.now()  # 失败时返回本地时间
