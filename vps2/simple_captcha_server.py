#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的验证码测试服务器
"""

from flask import Flask, render_template, Response, request, redirect, url_for, flash
import random
import string
import io

app = Flask(__name__)
app.secret_key = 'test_secret_key'

def generate_captcha_code(length=4):
    """生成验证码字符串"""
    chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
    return ''.join(random.choice(chars) for _ in range(length))

def create_simple_captcha_image(code):
    """创建简单的验证码图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 图片尺寸
        width, height = 120, 40
        
        # 创建图片
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 20)
            except:
                font = ImageFont.load_default()
        
        # 绘制验证码文字
        text_width = len(code) * 15  # 简单估算
        x = (width - text_width) // 2
        y = (height - 20) // 2
        
        # 为每个字符添加随机颜色和位置偏移
        for i, char in enumerate(code):
            char_x = x + i * 20
            char_y = y + random.randint(-3, 3)
            color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
            draw.text((char_x, char_y), char, fill=color, font=font)
        
        # 添加干扰线
        for _ in range(3):
            x1, y1 = random.randint(0, width), random.randint(0, height)
            x2, y2 = random.randint(0, width), random.randint(0, height)
            draw.line([(x1, y1), (x2, y2)], fill=(random.randint(100, 200), random.randint(100, 200), random.randint(100, 200)), width=1)
        
        # 添加噪点
        for _ in range(20):
            x, y = random.randint(0, width-1), random.randint(0, height-1)
            draw.point((x, y), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
        
        return image
    except ImportError:
        # 如果PIL不可用，创建一个简单的文本图片
        print("PIL not available, creating text-based captcha")
        from PIL import Image, ImageDraw
        image = Image.new('RGB', (120, 40), color='lightgray')
        draw = ImageDraw.Draw(image)
        draw.text((10, 10), code, fill='black')
        return image

# 存储当前验证码（实际应用中应该使用session或数据库）
current_captcha = None

@app.route('/')
def index():
    return '''
    <h1>验证码测试服务器</h1>
    <p><a href="/login">测试登录页面（带验证码）</a></p>
    <p><a href="/captcha">直接查看验证码图片</a></p>
    <p><a href="/test_captcha">测试验证码生成</a></p>
    '''

@app.route('/login', methods=['GET', 'POST'])
def login():
    global current_captcha
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        captcha_input = request.form.get('captcha', '').upper()
        
        # 验证验证码
        if current_captcha and captcha_input == current_captcha.upper():
            if username == 'admin' and password == 'admin':
                flash('登录成功！', 'success')
                return redirect(url_for('success'))
            else:
                flash('用户名或密码错误', 'danger')
        else:
            flash('验证码错误，请重新输入', 'danger')
    
    return render_template('login_with_captcha.html')

@app.route('/success')
def success():
    return '''
    <h1>登录成功！</h1>
    <p>验证码功能正常工作</p>
    <p><a href="/login">返回登录页面</a></p>
    '''

@app.route('/captcha')
def captcha():
    global current_captcha
    
    try:
        # 生成新的验证码
        current_captcha = generate_captcha_code()
        print(f"Generated captcha: {current_captcha}")
        
        # 创建验证码图片
        image = create_simple_captcha_image(current_captcha)
        
        # 将图片转换为字节流
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        return Response(img_buffer.getvalue(), mimetype='image/png')
        
    except Exception as e:
        print(f"Error generating captcha: {e}")
        # 返回错误图片
        try:
            from PIL import Image, ImageDraw
            error_image = Image.new('RGB', (120, 40), color='red')
            draw = ImageDraw.Draw(error_image)
            draw.text((10, 10), 'ERROR', fill='white')
            
            img_buffer = io.BytesIO()
            error_image.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            return Response(img_buffer.getvalue(), mimetype='image/png')
        except:
            return "Captcha generation failed", 500

@app.route('/test_captcha')
def test_captcha():
    """测试验证码生成"""
    try:
        code = generate_captcha_code()
        image = create_simple_captcha_image(code)
        
        # 保存到文件
        image.save('test_captcha_output.png')
        
        return f'''
        <h1>验证码测试</h1>
        <p>生成的验证码: <strong>{code}</strong></p>
        <p>图片已保存为: test_captcha_output.png</p>
        <p><img src="/captcha" alt="验证码" style="border: 1px solid #ccc;"></p>
        <p><a href="/">返回首页</a></p>
        <script>
            // 每3秒刷新一次验证码
            setInterval(function() {{
                document.querySelector('img').src = '/captcha?' + new Date().getTime();
            }}, 3000);
        </script>
        '''
    except Exception as e:
        return f'验证码测试失败: {str(e)}'

if __name__ == '__main__':
    print("启动验证码测试服务器...")
    print("访问 http://localhost:5000 查看测试页面")
    print("访问 http://localhost:5000/login 测试登录页面")
    print("访问 http://localhost:5000/captcha 直接查看验证码")
    print("按 Ctrl+C 停止服务器")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        print("可能的原因:")
        print("1. 端口5000被占用")
        print("2. PIL/Pillow库未安装")
        print("3. Flask库未安装")
        print("\n请运行以下命令安装依赖:")
        print("pip install Flask Pillow")
