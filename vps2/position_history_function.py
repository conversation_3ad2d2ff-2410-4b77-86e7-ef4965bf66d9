def get_history_deals_by_position(position_id, mt5_initialized=False, init_mt5=None):
    """
    获取特定持仓ID相关的历史交易记录
    """
    try:
        # 验证MT5连接状态
        if not mt5_initialized:
            if init_mt5 and callable(init_mt5):
                if not init_mt5():
                    logger.error(f"获取持仓ID {position_id} 的历史交易记录失败：MT5未初始化")
                    return []
            else:
                logger.error(f"获取持仓ID {position_id} 的历史交易记录失败：MT5未初始化，且未提供init_mt5函数")
                return []
        
        # 获取所有历史交易记录
        deals_total = mt5.history_deals_total()
        if deals_total <= 0:
            logger.info(f"没有历史交易记录")
            return []
            
        deals = mt5.history_deals_get(0, deals_total)
        if deals is None or len(deals) == 0:
            logger.info(f"获取历史交易记录失败，错误: {mt5.last_error()}")
            return []
        
        # 筛选特定持仓ID的交易记录
        position_deals = []
        for deal in deals:
            if deal.position_id == position_id:
                # 转换时间戳为datetime对象
                time_obj = datetime.fromtimestamp(deal.time)
                time_str = time_obj.isoformat()
                
                deal_info = {
                    'time': time_str,
                    'deal': deal.deal,
                    'order': deal.order,
                    'symbol': deal.symbol,
                    'type': deal.type,
                    'type_str': get_deal_type_string(deal.type),
                    'entry': deal.entry,
                    'entry_str': get_deal_entry_string(deal.entry),
                    'volume': deal.volume,
                    'price': deal.price,
                    'profit': deal.profit,
                    'commission': deal.commission,
                    'swap': deal.swap,
                    'fee': deal.fee,
                    'sl': deal.sl,
                    'tp': deal.tp,
                    'magic': deal.magic,
                    'comment': deal.comment,
                    'reason': deal.reason,
                    'reason_str': get_deal_reason_string(deal.reason),
                    'position_id': deal.position_id,
                    'external_id': deal.external_id
                }
                position_deals.append(deal_info)
        
        # 按时间排序
        position_deals.sort(key=lambda x: x['time'])
        logger.info(f"为持仓ID {position_id} 获取到 {len(position_deals)} 条历史交易记录")
        
        return position_deals
        
    except Exception as e:
        logger.error(f"获取持仓ID {position_id} 的历史交易记录时出错: {e}", exc_info=True)
        return []
