# VPS2交易系统启动脚本 (PowerShell版本)
# 自动切换到项目目录并启动主程序

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=======================================" -ForegroundColor Cyan
Write-Host "       VPS2 MT5交易系统启动器" -ForegroundColor Yellow
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# 获取脚本所在目录并切换到该目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# 显示当前目录
Write-Host "当前工作目录: $PWD" -ForegroundColor Green
Write-Host ""

# 检查Python是否可用
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "Python版本信息: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Python，请确保Python已正确安装并添加到PATH" -ForegroundColor Red
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 检查main.py是否存在
if (-not (Test-Path "main.py")) {
    Write-Host "错误: 未找到main.py文件" -ForegroundColor Red
    Write-Host "请确保脚本在正确的项目目录中运行" -ForegroundColor Red
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 创建日志目录（如果不存在）
if (-not (Test-Path "logs")) {
    Write-Host "创建日志目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Name "logs" | Out-Null
}

# 检查配置文件
if (-not (Test-Path "config.json")) {
    Write-Host "警告: 未找到config.json配置文件" -ForegroundColor Yellow
    if (Test-Path "config_fixed.json") {
        Write-Host "发现config_fixed.json，将其复制为config.json" -ForegroundColor Yellow
        Copy-Item "config_fixed.json" "config.json"
    }
}

# 检查依赖包
if (Test-Path "requirements.txt") {
    Write-Host "检查Python依赖包..." -ForegroundColor Yellow
    try {
        # 静默检查是否需要安装依赖
        $pipCheck = pip list --format=freeze 2>&1
        Write-Host "依赖检查完成" -ForegroundColor Green
    } catch {
        Write-Host "警告: 无法检查依赖包，请确保已安装requirements.txt中的包" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "启动VPS2交易系统..." -ForegroundColor Green
Write-Host "按 Ctrl+C 可以停止系统" -ForegroundColor Yellow
Write-Host ""

# 启动主程序
try {
    python main.py
} catch {
    Write-Host ""
    Write-Host "程序异常退出: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查日志文件获取详细信息" -ForegroundColor Red
    Write-Host ""
}

Write-Host ""
Write-Host "系统已关闭" -ForegroundColor Yellow
Read-Host "按任意键退出"
