def direct_market_order(symbol, operation, volume, comment=None):
    """
    直接以市场价格执行交易，不经过信号系统
    
    参数:
        symbol: 交易品种
        operation: 交易方向 ('buy' 或 'sell')
        volume: 交易量
        comment: 订单注释
    
    返回:
        字典，包含交易结果
    """
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法执行交易")
        return {'success': False, 'message': 'MT5未连接'}
    
    try:
        # 确保使用最新配置
        load_config()
        
        # 检查交易品种是否启用
        mt5_symbol = map_trading_pair(symbol)
        
        # 获取已启用的交易品种列表
        enabled_symbols = config.get('enabled_symbols', {})
        
        # 如果交易品种未启用，不执行交易
        if mt5_symbol not in enabled_symbols or not enabled_symbols.get(mt5_symbol, True):
            error_msg = f"交易品种 {mt5_symbol} 已被禁用，不执行交易"
            logger.warning(error_msg)
            return {'success': False, 'message': error_msg}
        
        # 检查是否启用了自动交易
        terminal_info = mt5.terminal_info()
        if terminal_info is not None and not terminal_info.trade_allowed:
            error_msg = "MT5自动交易功能未启用，无法执行交易"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}
        
        # 检查交易对是否有效
        if not mt5.symbol_select(mt5_symbol, True):
            error_msg = f"交易对 {mt5_symbol} 不可用或不存在"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}
        
        # 获取当前市场价格
        if operation.lower() == 'buy':
            price = mt5.symbol_info_tick(mt5_symbol).ask
            order_type = mt5.ORDER_TYPE_BUY
        else:
            price = mt5.symbol_info_tick(mt5_symbol).bid
            order_type = mt5.ORDER_TYPE_SELL
        
        # 设置交易请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": mt5_symbol,
            "volume": float(volume),
            "type": order_type,
            "price": price,
            "deviation": 10,
            "magic": 12345,
            "comment": comment if comment else "Direct market order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        
        logger.info(f"发送直接市场下单请求: {json.dumps(request, default=str)}")
        
        # 执行交易
        result = try_order_with_filling_modes(request)
        
        if result['success']:
            order_ticket = result['order']
            logger.info(f"直接市场下单成功，订单号: {order_ticket}")
            
            # 等待订单入场
            time.sleep(1)
            
            # 获取持仓信息，包括入场价格
            positions = mt5.positions_get(ticket=order_ticket)
            entry_price = None
            
            if positions and len(positions) > 0:
                position = positions[0]
                entry_price = position.price_open
                
                # 计算止损止盈价格
                sl_points = config.get('symbol_sl_points', {}).get(mt5_symbol, config.get('default_sl_points', 0))
                tp_points = config.get('symbol_tp_points', {}).get(mt5_symbol, config.get('default_tp_points', 0))
                
                sl = None
                tp = None
                
                if operation.lower() == 'buy':
                    if sl_points > 0:
                        sl = round_price(mt5_symbol, entry_price - (sl_points * mt5.symbol_info(mt5_symbol).point))
                    if tp_points > 0:
                        tp = round_price(mt5_symbol, entry_price + (tp_points * mt5.symbol_info(mt5_symbol).point))
                else:
                    if sl_points > 0:
                        sl = round_price(mt5_symbol, entry_price + (sl_points * mt5.symbol_info(mt5_symbol).point))
                    if tp_points > 0:
                        tp = round_price(mt5_symbol, entry_price - (tp_points * mt5.symbol_info(mt5_symbol).point))
                
                # 设置止损止盈
                if sl_points > 0 or tp_points > 0:
                    logger.info(f"设置订单 {order_ticket} 的止损止盈: SL={sl}, TP={tp}")
                    modify_result = modify_sl_tp(order_ticket, sl, tp)
                    if not modify_result or not modify_result.get('success', False):
                        logger.warning(f"设置止损止盈失败: {modify_result.get('message', '未知错误') if modify_result else '未知错误'}")
                
                # 保存订单信息到数据库
                order_info = {
                    'ticket': order_ticket,
                    'symbol': mt5_symbol,
                    'operation': operation.lower(),
                    'volume': volume,
                    'price': entry_price,
                    'sl': sl,
                    'tp': tp,
                    'signal_id': None  # 没有关联信号
                }
                store_order(order_info)

                # 发送Bark通知
                import bark_notifier
                bark_notifier.notify_trade_execution(order_info)

                return {
                    'success': True,
                    'ticket': order_ticket,
                    'price': entry_price,
                    'sl': sl,
                    'tp': tp,
                    'message': '直接市场下单成功'
                }
            else:
                logger.warning(f"下单成功但无法获取订单 {order_ticket} 的入场价格")
                return {
                    'success': True,
                    'ticket': order_ticket,
                    'message': '下单成功但无法获取入场价格'
                }
        else:
            error_msg = f"交易失败: {result.get('error', '未知错误')}"
            logger.error(error_msg)
            return {
                'success': False, 
                'message': error_msg
            }
    
    except Exception as e:
        logger.error(f"直接市场下单时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'message': f"下单时发生错误: {str(e)}"
        }
