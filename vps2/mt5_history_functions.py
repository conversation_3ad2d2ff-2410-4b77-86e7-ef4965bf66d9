#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5历史数据获取模块 - 获取历史交易记录和订单记录
"""

import MetaTrader5 as mt5
import logging
from datetime import datetime

# 使用与mt5_trader相同的日志记录器
logger = logging.getLogger(__name__)

def get_deal_type_string(deal_type):
    """获取交易类型的字符串描述"""
    deal_types = {
        mt5.DEAL_TYPE_BUY: "买入",
        mt5.DEAL_TYPE_SELL: "卖出",
        mt5.DEAL_TYPE_BALANCE: "余额",
        mt5.DEAL_TYPE_CREDIT: "信用",
        mt5.DEAL_TYPE_CHARGE: "手续费",
        mt5.DEAL_TYPE_CORRECTION: "校正",
        mt5.DEAL_TYPE_BONUS: "奖金",
        mt5.DEAL_TYPE_COMMISSION: "佣金",
        mt5.DEAL_TYPE_COMMISSION_DAILY: "每日佣金",
        mt5.DEAL_TYPE_COMMISSION_MONTHLY: "每月佣金",
        mt5.DEAL_TYPE_COMMISSION_AGENT_DAILY: "每日代理佣金",
        mt5.DEAL_TYPE_COMMISSION_AGENT_MONTHLY: "每月代理佣金",
        mt5.DEAL_TYPE_INTEREST: "利息",
        mt5.DEAL_TYPE_BUY_CANCELED: "买入取消",
        mt5.DEAL_TYPE_SELL_CANCELED: "卖出取消"
    }
    return deal_types.get(deal_type, f"未知类型({deal_type})")

def get_deal_entry_string(deal_entry):
    """获取交易入场类型的字符串描述"""
    deal_entries = {
        mt5.DEAL_ENTRY_IN: "入场",
        mt5.DEAL_ENTRY_OUT: "出场",
        mt5.DEAL_ENTRY_INOUT: "反向",
        mt5.DEAL_ENTRY_OUT_BY: "平仓"
    }
    return deal_entries.get(deal_entry, f"未知入场类型({deal_entry})")

def get_deal_reason_string(deal_reason):
    """获取交易原因的字符串描述"""
    deal_reasons = {
        mt5.DEAL_REASON_CLIENT: "客户端",
        mt5.DEAL_REASON_MOBILE: "移动端",
        mt5.DEAL_REASON_WEB: "网页端",
        mt5.DEAL_REASON_EXPERT: "EA",
        mt5.DEAL_REASON_SL: "止损",
        mt5.DEAL_REASON_TP: "止盈",
        mt5.DEAL_REASON_SO: "强制平仓",
        mt5.DEAL_REASON_ROLLOVER: "展期",
        mt5.DEAL_REASON_VMARGIN: "追加保证金",
        mt5.DEAL_REASON_SPLIT: "拆分"
    }
    return deal_reasons.get(deal_reason, f"未知原因({deal_reason})")

def get_order_state_string(order_state):
    """获取订单状态的字符串描述"""
    order_states = {
        mt5.ORDER_STATE_STARTED: "已开始",
        mt5.ORDER_STATE_PLACED: "已下单",
        mt5.ORDER_STATE_CANCELED: "已取消",
        mt5.ORDER_STATE_PARTIAL: "部分执行",
        mt5.ORDER_STATE_FILLED: "全部执行",
        mt5.ORDER_STATE_REJECTED: "已拒绝",
        mt5.ORDER_STATE_EXPIRED: "已过期",
        mt5.ORDER_STATE_REQUEST_ADD: "正在添加",
        mt5.ORDER_STATE_REQUEST_MODIFY: "正在修改",
        mt5.ORDER_STATE_REQUEST_CANCEL: "正在取消"
    }
    return order_states.get(order_state, f"未知状态({order_state})")

def get_order_type_string(order_type):
    """获取订单类型的字符串描述"""
    order_types = {
        mt5.ORDER_TYPE_BUY: "市价买入",
        mt5.ORDER_TYPE_SELL: "市价卖出",
        mt5.ORDER_TYPE_BUY_LIMIT: "限价买入",
        mt5.ORDER_TYPE_SELL_LIMIT: "限价卖出",
        mt5.ORDER_TYPE_BUY_STOP: "止损买入",
        mt5.ORDER_TYPE_SELL_STOP: "止损卖出",
        mt5.ORDER_TYPE_BUY_STOP_LIMIT: "止损限价买入",
        mt5.ORDER_TYPE_SELL_STOP_LIMIT: "止损限价卖出",
        mt5.ORDER_TYPE_CLOSE_BY: "对冲平仓"
    }
    return order_types.get(order_type, f"未知类型({order_type})")

def get_order_reason_string(order_reason):
    """获取订单创建原因的字符串描述"""
    order_reasons = {
        mt5.ORDER_REASON_CLIENT: "客户端",
        mt5.ORDER_REASON_MOBILE: "移动端",
        mt5.ORDER_REASON_WEB: "网页端",
        mt5.ORDER_REASON_EXPERT: "EA",
        mt5.ORDER_REASON_SL: "止损",
        mt5.ORDER_REASON_TP: "止盈",
        mt5.ORDER_REASON_SO: "强制平仓"
    }
    return order_reasons.get(order_reason, f"未知原因({order_reason})")

def get_history_deals(from_date=None, to_date=None, mt5_initialized=False, init_mt5=None):
    """获取历史交易记录

    Args:
        from_date: 开始日期 (datetime对象)
        to_date: 结束日期 (datetime对象)
        mt5_initialized: MT5是否已初始化
        init_mt5: MT5初始化函数

    Returns:
        list: 历史交易记录列表
    """
    # 检查MT5连接状态
    if not mt5_initialized:
        if init_mt5 and not init_mt5():
            logger.error("MT5未初始化，无法获取历史交易记录")
            return []
        elif not init_mt5:
            logger.error("MT5未初始化且没有提供初始化函数")
            return []

    try:
        # 记录查询参数
        if from_date and to_date:
            logger.info(f"查询历史交易记录，时间范围: {from_date} 到 {to_date}")
        else:
            logger.info("查询全部历史交易记录")

        # 设置查询参数
        if from_date and to_date:
            # 确保日期格式正确
            if isinstance(from_date, datetime):
                from_datetime = from_date
            else:
                from_datetime = datetime.combine(from_date, datetime.min.time())

            if isinstance(to_date, datetime):
                to_datetime = to_date
            else:
                to_datetime = datetime.combine(to_date, datetime.max.time())

            logger.debug(f"实际查询时间范围: {from_datetime} 到 {to_datetime}")
            deals = mt5.history_deals_get(from_datetime, to_datetime)
        else:
            # 不指定日期则获取全部历史
            deals_total = mt5.history_deals_total()
            logger.info(f"历史交易记录总数: {deals_total}")

            if deals_total > 0:
                # 限制获取数量，避免内存问题
                max_deals = min(deals_total, 10000)  # 最多获取10000条记录
                deals = mt5.history_deals_get(0, max_deals)
                logger.info(f"实际获取记录数: {max_deals}")
            else:
                deals = None

        # 检查获取结果
        if deals is None:
            error_code = mt5.last_error()
            logger.error(f"获取历史交易记录失败，错误代码: {error_code}")
            return []

        if len(deals) == 0:
            logger.info("查询时间范围内没有历史交易记录")
            return []
        
        # 处理交易记录
        deals_list = []
        buy_sell_count = 0  # 统计买卖交易数量
        closed_count = 0    # 统计已平仓交易数量

        for deal in deals:
            try:
                # 转换时间戳为datetime对象
                time_obj = datetime.fromtimestamp(deal.time)
                time_str = time_obj.isoformat()

                # 统计交易类型
                if deal.type in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL]:
                    buy_sell_count += 1
                    if deal.entry == mt5.DEAL_ENTRY_OUT:
                        closed_count += 1

                deal_info = {
                    'time': time_str,
                    'deal': deal.ticket,
                    'ticket': deal.ticket,
                    'order': deal.order,
                    'symbol': deal.symbol,
                    'type': deal.type,
                    'type_str': get_deal_type_string(deal.type),
                    'entry': deal.entry,
                    'entry_str': get_deal_entry_string(deal.entry),
                    'volume': deal.volume,
                    'price': deal.price,
                    'profit': deal.profit,
                    'commission': deal.commission,
                    'swap': deal.swap,
                    'fee': deal.fee,
                    'sl': getattr(deal, 'sl', 0.0),  # TradeDeal对象可能没有sl属性
                    'tp': getattr(deal, 'tp', 0.0),  # TradeDeal对象可能没有tp属性
                    'magic': deal.magic,
                    'comment': deal.comment,
                    'reason': deal.reason,
                    'reason_str': get_deal_reason_string(deal.reason),
                    'position_id': deal.position_id,
                    'external_id': deal.external_id
                }
                deals_list.append(deal_info)

            except Exception as e:
                logger.error(f"处理交易记录时出错: {e}, deal.ticket: {getattr(deal, 'ticket', 'N/A')}")
                continue

        logger.info(f"成功处理{len(deals_list)}条历史交易记录，其中买卖交易{buy_sell_count}条，已平仓交易{closed_count}条")
        return deals_list
    
    except Exception as e:
        logger.error(f"获取历史交易记录时发生错误: {e}", exc_info=True)
        return []

def get_closed_positions(from_date=None, to_date=None, mt5_initialized=False, init_mt5=None):
    """专门获取已平仓的持仓记录

    Args:
        from_date: 开始日期 (datetime对象)
        to_date: 结束日期 (datetime对象)
        mt5_initialized: MT5是否已初始化
        init_mt5: MT5初始化函数

    Returns:
        list: 已平仓持仓记录列表
    """
    logger.info("开始获取已平仓持仓记录")

    # 获取所有历史交易记录
    all_deals = get_history_deals(from_date, to_date, mt5_initialized, init_mt5)

    if not all_deals:
        logger.info("没有获取到历史交易记录")
        return []

    # 筛选已平仓的交易记录
    closed_positions = []
    for deal in all_deals:
        # 只处理买卖类型的交易，且是出场(平仓)的记录
        if (deal['type'] in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL] and
            deal['entry'] == mt5.DEAL_ENTRY_OUT and
            deal['profit'] is not None):

            # 添加额外的平仓信息
            closed_position = deal.copy()
            closed_position['is_closed'] = True
            closed_position['close_time'] = deal['time']
            closed_position['close_price'] = deal['price']

            closed_positions.append(closed_position)

            logger.debug(f"找到已平仓持仓: 持仓ID={deal['position_id']}, 交易对={deal['symbol']}, 盈亏={deal['profit']}")

    logger.info(f"筛选出{len(closed_positions)}条已平仓持仓记录")
    return closed_positions

def get_history_orders(from_date=None, to_date=None, mt5_initialized=False, init_mt5=None):
    """获取历史订单记录"""
    if not mt5_initialized and init_mt5 and not init_mt5():
        logger.error("MT5未初始化，无法获取历史订单记录")
        return []
    
    try:
        # 设置查询参数
        if from_date and to_date:
            # 指定了日期范围
            from_datetime = datetime.combine(from_date, datetime.min.time()) if isinstance(from_date, datetime) else from_date
            to_datetime = datetime.combine(to_date, datetime.max.time()) if isinstance(to_date, datetime) else to_date
            
            orders = mt5.history_orders_get(from_datetime, to_datetime)
        else:
            # 不指定日期则获取全部历史
            orders_total = mt5.history_orders_total()
            if orders_total > 0:
                orders = mt5.history_orders_get(0, orders_total)
            else:
                orders = None
        
        if orders is None:
            logger.info(f"获取历史订单记录失败，错误: {mt5.last_error()}")
            return []
        
        if len(orders) == 0:
            logger.info("没有历史订单记录")
            return []
        
        # 处理订单记录
        orders_list = []
        for order in orders:
            # 转换时间戳为datetime对象
            time_setup_obj = datetime.fromtimestamp(order.time_setup)
            time_done_obj = datetime.fromtimestamp(order.time_done) if order.time_done else None
            
            order_info = {
                'time_setup': time_setup_obj.isoformat(),
                'time_done': time_done_obj.isoformat() if time_done_obj else None,
                'ticket': order.ticket,
                'symbol': order.symbol,
                'type': order.type,
                'type_str': get_order_type_string(order.type),
                'state': order.state,
                'state_str': get_order_state_string(order.state),
                'volume_initial': order.volume_initial,
                'volume_current': order.volume_current,
                'price_open': order.price_open,
                'sl': order.sl,
                'tp': order.tp,
                'price_current': order.price_current,
                'price_stoplimit': order.price_stoplimit,
                'position_id': order.position_id,
                'position_by_id': order.position_by_id,
                'magic': order.magic,
                'reason': order.reason,
                'reason_str': get_order_reason_string(order.reason),
                'comment': order.comment,
                'external_id': order.external_id
            }
            orders_list.append(order_info)
        
        logger.info(f"获取到{len(orders_list)}条历史订单记录")
        return orders_list
    
    except Exception as e:
        logger.error(f"获取历史订单记录时发生错误: {e}", exc_info=True)
        return []
