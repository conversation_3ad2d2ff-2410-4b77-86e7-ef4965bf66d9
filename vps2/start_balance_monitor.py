#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动余额监测服务
"""

import balance_monitor
import time

def start_service():
    """启动余额监测服务"""
    print('=== 启动余额监测服务 ===')
    
    try:
        # 检查当前状态
        status = balance_monitor.get_balance_monitoring_status()
        print(f'当前服务状态: {"运行中" if status.get("running", False) else "未运行"}')
        
        if status.get("running", False):
            print('✅ 服务已在运行')
        else:
            # 启动余额监测服务
            balance_monitor.start_balance_monitoring()
            print('✅ 余额监测服务启动成功')
            
            # 等待一下再检查状态
            time.sleep(2)
            
            # 重新检查状态
            status = balance_monitor.get_balance_monitoring_status()
            print(f'启动后服务状态: {"运行中" if status.get("running", False) else "未运行"}')
        
        # 显示配置信息
        periodic_config = status.get("periodic_balance_notification", {})
        print(f'推送模式: {periodic_config.get("mode", "未设置")}')
        print(f'配置的时间点: {periodic_config.get("custom_times", [])}')
        print(f'时区设置: {periodic_config.get("timezone", "未设置")}')
        
        return True
        
    except Exception as e:
        print(f'❌ 启动失败: {e}')
        return False

def test_notification():
    """测试通知功能"""
    print('\n=== 测试通知功能 ===')
    
    try:
        # 手动发送一次定时通知
        balance_monitor.balance_monitor.send_periodic_balance_notification()
        print('✅ 测试通知已发送，请检查您的Bark设备')
        return True
        
    except Exception as e:
        print(f'❌ 测试通知失败: {e}')
        return False

def main():
    """主函数"""
    print('余额监测服务启动工具')
    print('=' * 50)
    
    # 启动服务
    if start_service():
        print('\n服务启动成功！')
        
        # 询问是否测试通知
        print('\n是否发送测试通知？')
        print('这将立即发送一次余额报告到您的Bark设备')
        
        # 自动发送测试通知
        test_notification()
        
        print('\n✅ 余额监测服务现在正在运行')
        print('系统将在以下时间点自动发送余额通知：')
        
        # 显示配置的时间点
        status = balance_monitor.get_balance_monitoring_status()
        custom_times = status.get("periodic_balance_notification", {}).get("custom_times", [])
        for time_point in custom_times:
            print(f'  - {time_point} (北京时间)')
        
        print('\n注意：')
        print('- 通知基于北京时间')
        print('- 系统会在每个时间点的±2分钟内发送通知')
        print('- 每个时间点每天只发送一次')
        print('- 如果系统重启，服务需要重新启动')
        
    else:
        print('\n❌ 服务启动失败，请检查错误信息')

if __name__ == "__main__":
    main()
