@echo off
REM 创建VPS2交易系统桌面快捷方式

echo 正在创建桌面快捷方式...

REM 获取当前脚本目录
set "SCRIPT_DIR=%~dp0"
set "BAT_FILE=%SCRIPT_DIR%start_vps2.bat"

REM 获取桌面路径
for /f "tokens=3*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set "DESKTOP=%%a %%b"

REM 创建快捷方式的VBS脚本
set "VBS_FILE=%TEMP%\create_shortcut.vbs"

echo Set WshShell = WScript.CreateObject("WScript.Shell") > "%VBS_FILE%"
echo Set Shortcut = WshShell.CreateShortcut("%DESKTOP%\VPS2交易系统.lnk") >> "%VBS_FILE%"
echo Shortcut.TargetPath = "%BAT_FILE%" >> "%VBS_FILE%"
echo Shortcut.WorkingDirectory = "%SCRIPT_DIR%" >> "%VBS_FILE%"
echo Shortcut.Description = "VPS2 MT5交易系统启动器" >> "%VBS_FILE%"
echo Shortcut.IconLocation = "%%SystemRoot%%\System32\shell32.dll,137" >> "%VBS_FILE%"
echo Shortcut.Save >> "%VBS_FILE%"

REM 执行VBS脚本创建快捷方式
cscript //nologo "%VBS_FILE%"

REM 清理临时文件
del "%VBS_FILE%"

if exist "%DESKTOP%\VPS2交易系统.lnk" (
    echo 桌面快捷方式创建成功！
    echo 快捷方式位置: %DESKTOP%\VPS2交易系统.lnk
) else (
    echo 快捷方式创建失败，请手动创建
    echo 目标文件: %BAT_FILE%
)

echo.
pause
