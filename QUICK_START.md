# 🚀 快速启动指南

## 一键启动MetaTrader5 + VPS2集成交易系统

### 📋 前置要求

- Docker 和 Docker Compose
- 有效的MT5交易账户
- 8GB+ 内存推荐

### ⚡ 快速启动（3步完成）

#### 1️⃣ 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置（设置您的用户名和密码）
nano .env
```

#### 2️⃣ 配置MT5账户
```bash
# 编辑VPS2配置文件
nano vps2/config.json
```

修改以下关键配置：
```json
{
    "mt5_login": "您的MT5账户号",
    "mt5_password": "您的MT5密码",
    "mt5_server": "您的MT5服务器名称",
    "enable_trading": true
}
```

#### 3️⃣ 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看启动日志
docker-compose logs -f
```

### 🌐 访问服务

启动完成后（首次启动需要5-10分钟），访问：

| 服务 | 地址 | 用途 |
|------|------|------|
| **MetaTrader5 VNC** | http://localhost:3000 | 图形界面操作MT5 |
| **VPS2 管理后台** | http://localhost:8080 | 交易系统管理 |
| **信号接收API** | http://localhost:9999 | 接收交易信号 |

### 🔑 默认登录信息

- **VNC界面**: 使用`.env`中设置的用户名密码
- **VPS2管理**: 用户名`admin`，密码`668899asd`

### 📡 发送交易信号示例

```bash
# 发送买入信号
curl -X POST http://localhost:9999/trade_signal \
  -H "Content-Type: application/json" \
  -d '{
    "trading_pair": "EURUSD",
    "signal_type": "buy",
    "close_price": 1.1000
  }'

# 发送卖出信号
curl -X POST http://localhost:9999/trade_signal \
  -H "Content-Type: application/json" \
  -d '{
    "trading_pair": "GBPUSD", 
    "signal_type": "sell",
    "close_price": 1.2500
  }'
```

### 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 进入容器调试
docker exec -it mt5_vps2 bash
```

### 📊 监控和日志

```bash
# VPS2服务日志
docker exec -it mt5_vps2 tail -f /vps2/logs/web_interface.log
docker exec -it mt5_vps2 tail -f /vps2/logs/signal_receiver.log
docker exec -it mt5_vps2 tail -f /vps2/logs/mt5_trader.log

# 检查端口状态
docker exec -it mt5_vps2 ss -tuln | grep -E "(3000|8001|8080|9999)"
```

### ⚠️ 重要提醒

1. **首次启动**: 需要5-10分钟下载和安装Wine、MetaTrader5
2. **MT5登录**: 在VNC界面中手动登录您的MT5账户
3. **交易风险**: 请在模拟账户上测试后再使用真实账户
4. **安全设置**: 修改默认密码，限制网络访问
5. **数据备份**: 定期备份配置和交易数据

### 🆘 故障排除

**问题**: 服务无法启动
```bash
# 检查端口占用
sudo netstat -tulpn | grep -E "(3000|8001|8080|9999)"

# 清理并重新启动
docker-compose down
docker-compose up -d
```

**问题**: MT5无法连接
- 检查网络连接
- 验证MT5账户信息
- 查看MT5日志

**问题**: VPS2服务异常
```bash
# 查看详细错误
docker-compose logs mt5_vps2

# 重启VPS2服务
docker exec -it mt5_vps2 /scripts/start_vps2.sh
```

### 📞 获取帮助

1. 查看完整文档: `README_VPS2_INTEGRATION.md`
2. 运行测试脚本: `python3 test_integration.py`
3. 检查日志文件获取详细错误信息

---

🎉 **恭喜！您的MetaTrader5 + VPS2集成交易系统已经准备就绪！**
