FROM ubuntu:22.04

# set version label
ARG BUILD_DATE
ARG VERSION
LABEL build_version="Metatrader Docker:- ${VERSION} Build-date:- ${BUILD_DATE}"
LABEL maintainer="gmartin"

ENV TITLE=Metatrader5
ENV WINEPREFIX="/config/.wine"
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:99

# Update package lists and upgrade packages
RUN apt-get update && apt-get upgrade -y

# Install required packages including Wine
RUN apt-get install -y \
    python3-pip \
    wget \
    sqlite3 \
    software-properties-common \
    gnupg2 \
    xvfb \
    x11vnc \
    fluxbox \
    && pip3 install --upgrade pip

# Add WineHQ repository key and APT source
RUN wget -q https://dl.winehq.org/wine-builds/winehq.key \
    && apt-key add winehq.key \
    && add-apt-repository 'deb https://dl.winehq.org/wine-builds/ubuntu/ jammy main' \
    && rm winehq.key

# Add i386 architecture and update package lists
RUN dpkg --add-architecture i386 \
    && apt-get update

# Install WineHQ stable package and dependencies
RUN apt-get install --install-recommends -y \
    winehq-stable \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install VPS2 Python dependencies
RUN pip3 install --no-cache-dir --break-system-packages \
    mt5linux \
    flask \
    werkzeug \
    requests \
    ntplib \
    pytz \
    Pillow

# Copy VPS2 project files
COPY /vps2 /vps2
WORKDIR /vps2

# Create logs directory for VPS2
RUN mkdir -p /vps2/logs

# Copy startup scripts
COPY /scripts /scripts
RUN chmod +x /scripts/*.sh /scripts/*.py

# Copy MetaTrader startup script (for reference)
COPY /Metatrader /Metatrader
RUN chmod +x /Metatrader/start.sh

# Expose all required ports
# 5900: VNC, 8001: MT5Linux RPyC, 8080: VPS2 Web, 9999: VPS2 Signal API
EXPOSE 5900 8001 8080 9999
VOLUME /config

# Set working directory
WORKDIR /

# Start the integrated system
CMD ["/scripts/start_integrated.sh"]
